{"artifacts": [{"path": "libs/SDL3/Release/SDL3.dll"}, {"path": "libs/SDL3/Release/SDL3.lib"}, {"path": "libs/SDL3/Release/SDL3.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "sdl_generic_link_dependency", "sdl_link_dependency", "add_compile_options", "target_compile_options", "SDL_AddCommonCompilerFlags", "target_compile_definitions", "sdl_compile_definitions", "target_include_directories", "sdl_include_directories", "target_precompile_headers", "target_compile_features", "target_sources", "sdl_glob_sources", "sdl_sources", "CheckHIDAPI"], "files": ["libs/SDL3/CMakeLists.txt", "libs/SDL3/cmake/sdlcommands.cmake", "libs/SDL3/cmake/sdlcompilers.cmake", "libs/SDL3/cmake/sdlchecks.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 448, "parent": 0}, {"command": 1, "file": 0, "line": 1235, "parent": 0}, {"command": 3, "file": 0, "line": 2105, "parent": 0}, {"command": 2, "file": 1, "line": 75, "parent": 3}, {"command": 1, "file": 1, "line": 49, "parent": 4}, {"command": 3, "file": 0, "line": 2159, "parent": 0}, {"command": 2, "file": 1, "line": 75, "parent": 6}, {"command": 1, "file": 1, "line": 49, "parent": 7}, {"command": 4, "file": 0, "line": 19, "parent": 0}, {"command": 6, "file": 0, "line": 450, "parent": 0}, {"command": 5, "file": 2, "line": 37, "parent": 10}, {"command": 8, "file": 0, "line": 3601, "parent": 0}, {"command": 7, "file": 1, "line": 109, "parent": 12}, {"command": 8, "file": 0, "line": 519, "parent": 0}, {"command": 7, "file": 1, "line": 109, "parent": 14}, {"command": 8, "file": 0, "line": 650, "parent": 0}, {"command": 7, "file": 1, "line": 109, "parent": 16}, {"command": 10, "file": 0, "line": 520, "parent": 0}, {"command": 9, "file": 1, "line": 157, "parent": 18}, {"command": 9, "file": 0, "line": 3556, "parent": 0}, {"command": 10, "file": 0, "line": 530, "parent": 0}, {"command": 9, "file": 1, "line": 157, "parent": 21}, {"command": 11, "file": 0, "line": 3573, "parent": 0}, {"command": 12, "file": 0, "line": 453, "parent": 0}, {"command": 14, "file": 0, "line": 1192, "parent": 0}, {"command": 13, "file": 1, "line": 11, "parent": 25}, {"command": 14, "file": 0, "line": 1261, "parent": 0}, {"command": 13, "file": 1, "line": 11, "parent": 27}, {"command": 14, "file": 0, "line": 1267, "parent": 0}, {"command": 13, "file": 1, "line": 11, "parent": 29}, {"command": 14, "file": 0, "line": 1277, "parent": 0}, {"command": 13, "file": 1, "line": 11, "parent": 31}, {"command": 14, "file": 0, "line": 1309, "parent": 0}, {"command": 13, "file": 1, "line": 11, "parent": 33}, {"command": 14, "file": 0, "line": 1316, "parent": 0}, {"command": 13, "file": 1, "line": 11, "parent": 35}, {"command": 14, "file": 0, "line": 1930, "parent": 0}, {"command": 13, "file": 1, "line": 11, "parent": 37}, {"command": 14, "file": 0, "line": 1931, "parent": 0}, {"command": 13, "file": 1, "line": 11, "parent": 39}, {"command": 14, "file": 0, "line": 1932, "parent": 0}, {"command": 13, "file": 1, "line": 11, "parent": 41}, {"command": 14, "file": 0, "line": 1933, "parent": 0}, {"command": 13, "file": 1, "line": 11, "parent": 43}, {"command": 14, "file": 0, "line": 1945, "parent": 0}, {"command": 13, "file": 1, "line": 11, "parent": 45}, {"command": 14, "file": 0, "line": 2026, "parent": 0}, {"command": 13, "file": 1, "line": 11, "parent": 47}, {"command": 14, "file": 0, "line": 2033, "parent": 0}, {"command": 13, "file": 1, "line": 11, "parent": 49}, {"command": 14, "file": 0, "line": 2040, "parent": 0}, {"command": 13, "file": 1, "line": 11, "parent": 51}, {"command": 14, "file": 0, "line": 2041, "parent": 0}, {"command": 13, "file": 1, "line": 11, "parent": 53}, {"command": 15, "file": 0, "line": 2063, "parent": 0}, {"command": 13, "file": 1, "line": 23, "parent": 55}, {"command": 14, "file": 0, "line": 2079, "parent": 0}, {"command": 13, "file": 1, "line": 11, "parent": 57}, {"command": 15, "file": 0, "line": 2084, "parent": 0}, {"command": 13, "file": 1, "line": 23, "parent": 59}, {"command": 14, "file": 0, "line": 2088, "parent": 0}, {"command": 13, "file": 1, "line": 11, "parent": 61}, {"command": 14, "file": 0, "line": 2092, "parent": 0}, {"command": 13, "file": 1, "line": 11, "parent": 63}, {"command": 14, "file": 0, "line": 2099, "parent": 0}, {"command": 13, "file": 1, "line": 11, "parent": 65}, {"command": 14, "file": 0, "line": 2101, "parent": 0}, {"command": 13, "file": 1, "line": 11, "parent": 67}, {"command": 14, "file": 0, "line": 2108, "parent": 0}, {"command": 13, "file": 1, "line": 11, "parent": 69}, {"command": 14, "file": 0, "line": 2112, "parent": 0}, {"command": 13, "file": 1, "line": 11, "parent": 71}, {"command": 14, "file": 0, "line": 2116, "parent": 0}, {"command": 13, "file": 1, "line": 11, "parent": 73}, {"command": 14, "file": 0, "line": 2145, "parent": 0}, {"command": 13, "file": 1, "line": 11, "parent": 75}, {"command": 16, "file": 0, "line": 2150, "parent": 0}, {"command": 14, "file": 3, "line": 1171, "parent": 77}, {"command": 13, "file": 1, "line": 11, "parent": 78}, {"command": 14, "file": 3, "line": 1172, "parent": 77}, {"command": 13, "file": 1, "line": 11, "parent": 80}, {"command": 14, "file": 0, "line": 2154, "parent": 0}, {"command": 13, "file": 1, "line": 11, "parent": 82}, {"command": 14, "file": 0, "line": 2176, "parent": 0}, {"command": 13, "file": 1, "line": 11, "parent": 84}, {"command": 14, "file": 0, "line": 2187, "parent": 0}, {"command": 13, "file": 1, "line": 11, "parent": 86}, {"command": 14, "file": 0, "line": 2192, "parent": 0}, {"command": 13, "file": 1, "line": 11, "parent": 88}, {"command": 15, "file": 0, "line": 3038, "parent": 0}, {"command": 13, "file": 1, "line": 23, "parent": 90}, {"command": 15, "file": 0, "line": 3040, "parent": 0}, {"command": 13, "file": 1, "line": 23, "parent": 92}, {"command": 15, "file": 0, "line": 3053, "parent": 0}, {"command": 13, "file": 1, "line": 23, "parent": 94}, {"command": 15, "file": 0, "line": 3061, "parent": 0}, {"command": 13, "file": 1, "line": 23, "parent": 96}, {"command": 14, "file": 0, "line": 3063, "parent": 0}, {"command": 13, "file": 1, "line": 11, "parent": 98}, {"command": 14, "file": 0, "line": 3115, "parent": 0}, {"command": 13, "file": 1, "line": 11, "parent": 100}, {"command": 14, "file": 0, "line": 3121, "parent": 0}, {"command": 13, "file": 1, "line": 11, "parent": 102}, {"command": 14, "file": 0, "line": 3130, "parent": 0}, {"command": 13, "file": 1, "line": 11, "parent": 104}, {"command": 14, "file": 0, "line": 3135, "parent": 0}, {"command": 13, "file": 1, "line": 11, "parent": 106}, {"command": 14, "file": 0, "line": 3230, "parent": 0}, {"command": 13, "file": 1, "line": 11, "parent": 108}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD"}, {"backtrace": 9, "fragment": "/utf-8"}, {"backtrace": 11, "fragment": "/W3"}, {"fragment": "\"/YcD:/<PERSON> Work All/CPP/f0/build/libs/SDL3/CMakeFiles/SDL3-shared.dir/Release/cmake_pch.hxx\" \"/FpD:/Frank Work All/CPP/f0/build/libs/SDL3/SDL3-shared.dir/Release/cmake_pch.cxx.pch\" \"/FID:/Frank Work All/CPP/f0/build/libs/SDL3/CMakeFiles/SDL3-shared.dir/Release/cmake_pch.hxx\""}], "defines": [{"define": "DLL_EXPORT"}, {"backtrace": 13, "define": "SDL_BUILD_MAJOR_VERSION=3"}, {"backtrace": 13, "define": "SDL_BUILD_MICRO_VERSION=0"}, {"backtrace": 13, "define": "SDL_BUILD_MINOR_VERSION=3"}, {"backtrace": 15, "define": "USING_GENERATED_CONFIG_H"}, {"backtrace": 17, "define": "_CRT_NONSTDC_NO_DEPRECATE"}, {"backtrace": 17, "define": "_CRT_SECURE_NO_DEPRECATE"}, {"backtrace": 17, "define": "_CRT_SECURE_NO_WARNINGS"}], "includes": [{"backtrace": 19, "path": "D:/<PERSON> All/CPP/f0/build/libs/SDL3/include-config-release/build_config"}, {"backtrace": 19, "path": "D:/<PERSON> All/CPP/f0/build/libs/SDL3/include-revision"}, {"backtrace": 19, "path": "D:/<PERSON> All/CPP/f0/libs/SDL3/include"}, {"backtrace": 20, "path": "D:/<PERSON>/CPP/f0/libs/SDL3/src"}, {"backtrace": 22, "isSystem": true, "path": "D:/<PERSON> All/CPP/f0/libs/SDL3/src/video/khronos"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "precompileHeaders": [{"backtrace": 23, "header": "D:/Frank Work All/CPP/f0/libs/SDL3/src/SDL_internal.h"}], "sourceIndexes": [0]}, {"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /O2 /Ob2 /DNDEBUG -MD"}, {"backtrace": 9, "fragment": "/utf-8"}, {"backtrace": 11, "fragment": "/W3"}, {"fragment": "\"/YcD:/<PERSON> Work All/CPP/f0/build/libs/SDL3/CMakeFiles/SDL3-shared.dir/Release/cmake_pch.h\" \"/FpD:/Frank Work All/CPP/f0/build/libs/SDL3/SDL3-shared.dir/Release/cmake_pch.c.pch\" \"/FID:/Frank Work All/CPP/f0/build/libs/SDL3/CMakeFiles/SDL3-shared.dir/Release/cmake_pch.h\""}], "defines": [{"define": "DLL_EXPORT"}, {"backtrace": 13, "define": "SDL_BUILD_MAJOR_VERSION=3"}, {"backtrace": 13, "define": "SDL_BUILD_MICRO_VERSION=0"}, {"backtrace": 13, "define": "SDL_BUILD_MINOR_VERSION=3"}, {"backtrace": 15, "define": "USING_GENERATED_CONFIG_H"}, {"backtrace": 17, "define": "_CRT_NONSTDC_NO_DEPRECATE"}, {"backtrace": 17, "define": "_CRT_SECURE_NO_DEPRECATE"}, {"backtrace": 17, "define": "_CRT_SECURE_NO_WARNINGS"}], "includes": [{"backtrace": 19, "path": "D:/<PERSON> All/CPP/f0/build/libs/SDL3/include-config-release/build_config"}, {"backtrace": 19, "path": "D:/<PERSON> All/CPP/f0/build/libs/SDL3/include-revision"}, {"backtrace": 19, "path": "D:/<PERSON> All/CPP/f0/libs/SDL3/include"}, {"backtrace": 20, "path": "D:/<PERSON>/CPP/f0/libs/SDL3/src"}, {"backtrace": 22, "isSystem": true, "path": "D:/<PERSON> All/CPP/f0/libs/SDL3/src/video/khronos"}], "language": "C", "languageStandard": {"backtraces": [24], "standard": "99"}, "precompileHeaders": [{"backtrace": 23, "header": "D:/Frank Work All/CPP/f0/libs/SDL3/src/SDL_internal.h"}], "sourceIndexes": [1]}, {"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /O2 /Ob2 /DNDEBUG -MD"}, {"backtrace": 9, "fragment": "/utf-8"}, {"backtrace": 11, "fragment": "/W3"}, {"fragment": "\"/YuD:/<PERSON> Work All/CPP/f0/build/libs/SDL3/CMakeFiles/SDL3-shared.dir/Release/cmake_pch.h\" \"/FpD:/Frank Work All/CPP/f0/build/libs/SDL3/SDL3-shared.dir/Release/cmake_pch.c.pch\" \"/FID:/Frank Work All/CPP/f0/build/libs/SDL3/CMakeFiles/SDL3-shared.dir/Release/cmake_pch.h\""}], "defines": [{"define": "DLL_EXPORT"}, {"backtrace": 13, "define": "SDL_BUILD_MAJOR_VERSION=3"}, {"backtrace": 13, "define": "SDL_BUILD_MICRO_VERSION=0"}, {"backtrace": 13, "define": "SDL_BUILD_MINOR_VERSION=3"}, {"backtrace": 15, "define": "USING_GENERATED_CONFIG_H"}, {"backtrace": 17, "define": "_CRT_NONSTDC_NO_DEPRECATE"}, {"backtrace": 17, "define": "_CRT_SECURE_NO_DEPRECATE"}, {"backtrace": 17, "define": "_CRT_SECURE_NO_WARNINGS"}], "includes": [{"backtrace": 19, "path": "D:/<PERSON> All/CPP/f0/build/libs/SDL3/include-config-release/build_config"}, {"backtrace": 19, "path": "D:/<PERSON> All/CPP/f0/build/libs/SDL3/include-revision"}, {"backtrace": 19, "path": "D:/<PERSON> All/CPP/f0/libs/SDL3/include"}, {"backtrace": 20, "path": "D:/<PERSON>/CPP/f0/libs/SDL3/src"}, {"backtrace": 22, "isSystem": true, "path": "D:/<PERSON> All/CPP/f0/libs/SDL3/src/video/khronos"}], "language": "C", "languageStandard": {"backtraces": [24], "standard": "99"}, "precompileHeaders": [{"backtrace": 23, "header": "D:/Frank Work All/CPP/f0/libs/SDL3/src/SDL_internal.h"}], "sourceIndexes": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239]}, {"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /O2 /Ob2 /DNDEBUG -MD"}, {"backtrace": 9, "fragment": "/utf-8"}, {"backtrace": 11, "fragment": "/W3"}], "defines": [{"define": "DLL_EXPORT"}, {"backtrace": 13, "define": "SDL_BUILD_MAJOR_VERSION=3"}, {"backtrace": 13, "define": "SDL_BUILD_MICRO_VERSION=0"}, {"backtrace": 13, "define": "SDL_BUILD_MINOR_VERSION=3"}, {"backtrace": 15, "define": "USING_GENERATED_CONFIG_H"}, {"backtrace": 17, "define": "_CRT_NONSTDC_NO_DEPRECATE"}, {"backtrace": 17, "define": "_CRT_SECURE_NO_DEPRECATE"}, {"backtrace": 17, "define": "_CRT_SECURE_NO_WARNINGS"}], "includes": [{"backtrace": 19, "path": "D:/<PERSON> All/CPP/f0/build/libs/SDL3/include-config-release/build_config"}, {"backtrace": 19, "path": "D:/<PERSON> All/CPP/f0/build/libs/SDL3/include-revision"}, {"backtrace": 19, "path": "D:/<PERSON> All/CPP/f0/libs/SDL3/include"}, {"backtrace": 20, "path": "D:/<PERSON>/CPP/f0/libs/SDL3/src"}, {"backtrace": 22, "isSystem": true, "path": "D:/<PERSON> All/CPP/f0/libs/SDL3/src/video/khronos"}], "language": "C", "languageStandard": {"backtraces": [24], "standard": "99"}, "precompileHeaders": [{"backtrace": 23, "header": "D:/Frank Work All/CPP/f0/libs/SDL3/src/SDL_internal.h"}], "sourceIndexes": [25]}, {"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /O2 /Ob2 /DNDEBUG -MD"}, {"backtrace": 9, "fragment": "/utf-8"}, {"backtrace": 11, "fragment": "/W3"}, {"fragment": " /GL-"}, {"fragment": "\"/YuD:/<PERSON> Work All/CPP/f0/build/libs/SDL3/CMakeFiles/SDL3-shared.dir/Release/cmake_pch.h\" \"/FpD:/Frank Work All/CPP/f0/build/libs/SDL3/SDL3-shared.dir/Release/cmake_pch.c.pch\" \"/FID:/Frank Work All/CPP/f0/build/libs/SDL3/CMakeFiles/SDL3-shared.dir/Release/cmake_pch.h\""}], "defines": [{"define": "DLL_EXPORT"}, {"backtrace": 13, "define": "SDL_BUILD_MAJOR_VERSION=3"}, {"backtrace": 13, "define": "SDL_BUILD_MICRO_VERSION=0"}, {"backtrace": 13, "define": "SDL_BUILD_MINOR_VERSION=3"}, {"backtrace": 15, "define": "USING_GENERATED_CONFIG_H"}, {"backtrace": 17, "define": "_CRT_NONSTDC_NO_DEPRECATE"}, {"backtrace": 17, "define": "_CRT_SECURE_NO_DEPRECATE"}, {"backtrace": 17, "define": "_CRT_SECURE_NO_WARNINGS"}], "includes": [{"backtrace": 19, "path": "D:/<PERSON> All/CPP/f0/build/libs/SDL3/include-config-release/build_config"}, {"backtrace": 19, "path": "D:/<PERSON> All/CPP/f0/build/libs/SDL3/include-revision"}, {"backtrace": 19, "path": "D:/<PERSON> All/CPP/f0/libs/SDL3/include"}, {"backtrace": 20, "path": "D:/<PERSON>/CPP/f0/libs/SDL3/src"}, {"backtrace": 22, "isSystem": true, "path": "D:/<PERSON> All/CPP/f0/libs/SDL3/src/video/khronos"}], "language": "C", "languageStandard": {"backtraces": [24], "standard": "99"}, "precompileHeaders": [{"backtrace": 23, "header": "D:/Frank Work All/CPP/f0/libs/SDL3/src/SDL_internal.h"}], "sourceIndexes": [101]}, {"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD"}, {"backtrace": 9, "fragment": "/utf-8"}, {"backtrace": 11, "fragment": "/W3"}, {"fragment": "\"/YuD:/<PERSON> Work All/CPP/f0/build/libs/SDL3/CMakeFiles/SDL3-shared.dir/Release/cmake_pch.hxx\" \"/FpD:/Frank Work All/CPP/f0/build/libs/SDL3/SDL3-shared.dir/Release/cmake_pch.cxx.pch\" \"/FID:/Frank Work All/CPP/f0/build/libs/SDL3/CMakeFiles/SDL3-shared.dir/Release/cmake_pch.hxx\""}], "defines": [{"define": "DLL_EXPORT"}, {"backtrace": 13, "define": "SDL_BUILD_MAJOR_VERSION=3"}, {"backtrace": 13, "define": "SDL_BUILD_MICRO_VERSION=0"}, {"backtrace": 13, "define": "SDL_BUILD_MINOR_VERSION=3"}, {"backtrace": 15, "define": "USING_GENERATED_CONFIG_H"}, {"backtrace": 17, "define": "_CRT_NONSTDC_NO_DEPRECATE"}, {"backtrace": 17, "define": "_CRT_SECURE_NO_DEPRECATE"}, {"backtrace": 17, "define": "_CRT_SECURE_NO_WARNINGS"}], "includes": [{"backtrace": 19, "path": "D:/<PERSON> All/CPP/f0/build/libs/SDL3/include-config-release/build_config"}, {"backtrace": 19, "path": "D:/<PERSON> All/CPP/f0/build/libs/SDL3/include-revision"}, {"backtrace": 19, "path": "D:/<PERSON> All/CPP/f0/libs/SDL3/include"}, {"backtrace": 20, "path": "D:/<PERSON>/CPP/f0/libs/SDL3/src"}, {"backtrace": 22, "isSystem": true, "path": "D:/<PERSON> All/CPP/f0/libs/SDL3/src/video/khronos"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "precompileHeaders": [{"backtrace": 23, "header": "D:/Frank Work All/CPP/f0/libs/SDL3/src/SDL_internal.h"}], "sourceIndexes": [149, 150, 171]}, {"compileCommandFragments": [{"fragment": "-DWIN32"}], "defines": [{"define": "DLL_EXPORT"}, {"backtrace": 13, "define": "SDL_BUILD_MAJOR_VERSION=3"}, {"backtrace": 13, "define": "SDL_BUILD_MICRO_VERSION=0"}, {"backtrace": 13, "define": "SDL_BUILD_MINOR_VERSION=3"}, {"backtrace": 15, "define": "USING_GENERATED_CONFIG_H"}, {"backtrace": 17, "define": "_CRT_NONSTDC_NO_DEPRECATE"}, {"backtrace": 17, "define": "_CRT_SECURE_NO_DEPRECATE"}, {"backtrace": 17, "define": "_CRT_SECURE_NO_WARNINGS"}], "includes": [{"backtrace": 19, "path": "D:/<PERSON> All/CPP/f0/build/libs/SDL3/include-config-release/build_config"}, {"backtrace": 19, "path": "D:/<PERSON> All/CPP/f0/build/libs/SDL3/include-revision"}, {"backtrace": 19, "path": "D:/<PERSON> All/CPP/f0/libs/SDL3/include"}, {"backtrace": 20, "path": "D:/<PERSON>/CPP/f0/libs/SDL3/src"}, {"backtrace": 22, "isSystem": true, "path": "D:/<PERSON> All/CPP/f0/libs/SDL3/src/video/khronos"}], "language": "RC", "sourceIndexes": [224]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}, {"backtrace": 2, "id": "SDL_uclibc::@1536e03098708b12d592"}], "id": "SDL3-shared::@1536e03098708b12d592", "link": {"commandFragments": [{"fragment": "/machine:x64 /INCREMENTAL:NO", "role": "flags"}, {"backtrace": 2, "fragment": "Release\\SDL_uclibc.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "kernel32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "gdi32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "winmm.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "imm32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "ole32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "oleaut32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "version.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "uuid.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "advapi32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "setupapi.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "shell32.lib", "role": "libraries"}, {"backtrace": 8, "fragment": "dinput8.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "SDL3-shared", "nameOnDisk": "SDL3.dll", "paths": {"build": "libs/SDL3", "source": "libs/SDL3"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239]}, {"name": "Precompile Header File", "sourceIndexes": [240, 241, 242, 243, 244, 245, 246, 247]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "path": "build/libs/SDL3/CMakeFiles/SDL3-shared.dir/cmake_pch.cxx", "sourceGroupIndex": 0}, {"backtrace": 0, "compileGroupIndex": 1, "path": "build/libs/SDL3/CMakeFiles/SDL3-shared.dir/cmake_pch.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/SDL.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/SDL_assert.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/SDL_error.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/SDL_guid.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/SDL_hashtable.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/SDL_hints.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/SDL_list.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/SDL_log.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/SDL_properties.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/SDL_utils.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/atomic/SDL_atomic.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/atomic/SDL_spinlock.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/audio/SDL_audio.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/audio/SDL_audiocvt.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/audio/SDL_audiodev.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/audio/SDL_audioqueue.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/audio/SDL_audioresample.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/audio/SDL_audiotypecvt.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/audio/SDL_mixer.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/audio/SDL_wave.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/camera/SDL_camera.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/core/SDL_core_unsupported.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/cpuinfo/SDL_cpuinfo.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 3, "path": "libs/SDL3/src/dynapi/SDL_dynapi.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/events/SDL_categories.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/events/SDL_clipboardevents.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/events/SDL_displayevents.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/events/SDL_dropevents.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/events/SDL_events.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/events/SDL_eventwatch.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/events/SDL_keyboard.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/events/SDL_keymap.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/events/SDL_keysym_to_keycode.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/events/SDL_keysym_to_scancode.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/events/SDL_mouse.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/events/SDL_pen.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/events/SDL_quit.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/events/SDL_scancode_tables.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/events/SDL_touch.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/events/SDL_windowevents.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/events/imKStoUCS.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/filesystem/SDL_filesystem.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/gpu/SDL_gpu.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/haptic/SDL_haptic.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/hidapi/SDL_hidapi.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/io/SDL_asyncio.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/io/SDL_iostream.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/io/generic/SDL_asyncio_generic.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/joystick/SDL_gamepad.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/joystick/SDL_joystick.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/joystick/SDL_steam_virtual_gamepad.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/joystick/controller_type.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/locale/SDL_locale.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/main/SDL_main_callbacks.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/main/SDL_runapp.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/misc/SDL_url.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/power/SDL_power.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/render/SDL_d3dmath.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/render/SDL_render.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/render/SDL_render_unsupported.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/render/SDL_yuv_sw.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/render/direct3d/SDL_render_d3d.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/render/direct3d/SDL_shaders_d3d.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/render/direct3d11/SDL_render_d3d11.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/render/direct3d11/SDL_shaders_d3d11.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/render/direct3d12/SDL_render_d3d12.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/render/direct3d12/SDL_shaders_d3d12.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/render/gpu/SDL_pipeline_gpu.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/render/gpu/SDL_render_gpu.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/render/gpu/SDL_shaders_gpu.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/render/ngage/SDL_render_ngage.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/render/opengl/SDL_render_gl.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/render/opengl/SDL_shaders_gl.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/render/opengles2/SDL_render_gles2.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/render/opengles2/SDL_shaders_gles2.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/render/ps2/SDL_render_ps2.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/render/psp/SDL_render_psp.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/render/software/SDL_blendfillrect.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/render/software/SDL_blendline.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/render/software/SDL_blendpoint.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/render/software/SDL_drawline.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/render/software/SDL_drawpoint.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/render/software/SDL_render_sw.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/render/software/SDL_rotate.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/render/software/SDL_triangle.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/render/vitagxm/SDL_render_vita_gxm.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/render/vitagxm/SDL_render_vita_gxm_memory.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/render/vitagxm/SDL_render_vita_gxm_tools.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/render/vulkan/SDL_render_vulkan.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/render/vulkan/SDL_shaders_vulkan.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/sensor/SDL_sensor.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/stdlib/SDL_crc16.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/stdlib/SDL_crc32.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/stdlib/SDL_getenv.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/stdlib/SDL_iconv.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/stdlib/SDL_malloc.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/stdlib/SDL_memcpy.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/stdlib/SDL_memmove.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/stdlib/SDL_memset.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 4, "path": "libs/SDL3/src/stdlib/SDL_mslibc.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/stdlib/SDL_murmur3.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/stdlib/SDL_qsort.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/stdlib/SDL_random.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/stdlib/SDL_stdlib.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/stdlib/SDL_string.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/stdlib/SDL_strtokr.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/storage/SDL_storage.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/thread/SDL_thread.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/time/SDL_time.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/timer/SDL_timer.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/SDL_RLEaccel.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/SDL_blit.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/SDL_blit_0.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/SDL_blit_1.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/SDL_blit_A.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/SDL_blit_N.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/SDL_blit_auto.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/SDL_blit_copy.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/SDL_blit_slow.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/SDL_bmp.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/SDL_clipboard.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/SDL_egl.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/SDL_fillrect.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/SDL_pixels.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/SDL_rect.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/SDL_stb.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/SDL_stretch.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/SDL_surface.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/SDL_video.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/SDL_video_unsupported.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/SDL_vulkan_utils.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/SDL_yuv.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/yuv2rgb/yuv_rgb_lsx.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/yuv2rgb/yuv_rgb_sse.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/yuv2rgb/yuv_rgb_std.c", "sourceGroupIndex": 0}, {"backtrace": 28, "compileGroupIndex": 2, "path": "libs/SDL3/src/audio/dummy/SDL_dummyaudio.c", "sourceGroupIndex": 0}, {"backtrace": 30, "compileGroupIndex": 2, "path": "libs/SDL3/src/audio/disk/SDL_diskaudio.c", "sourceGroupIndex": 0}, {"backtrace": 32, "compileGroupIndex": 2, "path": "libs/SDL3/src/camera/dummy/SDL_camera_dummy.c", "sourceGroupIndex": 0}, {"backtrace": 34, "compileGroupIndex": 2, "path": "libs/SDL3/src/joystick/virtual/SDL_virtualjoystick.c", "sourceGroupIndex": 0}, {"backtrace": 36, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/dummy/SDL_nullevents.c", "sourceGroupIndex": 0}, {"backtrace": 36, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/dummy/SDL_nullframebuffer.c", "sourceGroupIndex": 0}, {"backtrace": 36, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/dummy/SDL_nullvideo.c", "sourceGroupIndex": 0}, {"backtrace": 38, "compileGroupIndex": 2, "path": "libs/SDL3/src/core/windows/SDL_hid.c", "sourceGroupIndex": 0}, {"backtrace": 38, "compileGroupIndex": 2, "path": "libs/SDL3/src/core/windows/SDL_immdevice.c", "sourceGroupIndex": 0}, {"backtrace": 38, "compileGroupIndex": 2, "path": "libs/SDL3/src/core/windows/SDL_windows.c", "sourceGroupIndex": 0}, {"backtrace": 38, "compileGroupIndex": 2, "path": "libs/SDL3/src/core/windows/SDL_xinput.c", "sourceGroupIndex": 0}, {"backtrace": 38, "compileGroupIndex": 2, "path": "libs/SDL3/src/core/windows/pch.c", "sourceGroupIndex": 0}, {"backtrace": 40, "compileGroupIndex": 5, "path": "libs/SDL3/src/core/windows/SDL_gameinput.cpp", "sourceGroupIndex": 0}, {"backtrace": 40, "compileGroupIndex": 5, "path": "libs/SDL3/src/core/windows/pch_cpp.cpp", "sourceGroupIndex": 0}, {"backtrace": 42, "compileGroupIndex": 2, "path": "libs/SDL3/src/main/windows/SDL_sysmain_runapp.c", "sourceGroupIndex": 0}, {"backtrace": 44, "compileGroupIndex": 2, "path": "libs/SDL3/src/io/windows/SDL_asyncio_windows_ioring.c", "sourceGroupIndex": 0}, {"backtrace": 46, "compileGroupIndex": 2, "path": "libs/SDL3/src/misc/windows/SDL_sysurl.c", "sourceGroupIndex": 0}, {"backtrace": 48, "compileGroupIndex": 2, "path": "libs/SDL3/src/audio/directsound/SDL_directsound.c", "sourceGroupIndex": 0}, {"backtrace": 50, "compileGroupIndex": 2, "path": "libs/SDL3/src/audio/wasapi/SDL_wasapi.c", "sourceGroupIndex": 0}, {"backtrace": 52, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/windows/SDL_surface_utils.c", "sourceGroupIndex": 0}, {"backtrace": 52, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/windows/SDL_windowsclipboard.c", "sourceGroupIndex": 0}, {"backtrace": 52, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/windows/SDL_windowsevents.c", "sourceGroupIndex": 0}, {"backtrace": 52, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/windows/SDL_windowsframebuffer.c", "sourceGroupIndex": 0}, {"backtrace": 52, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/windows/SDL_windowskeyboard.c", "sourceGroupIndex": 0}, {"backtrace": 52, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/windows/SDL_windowsmessagebox.c", "sourceGroupIndex": 0}, {"backtrace": 52, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/windows/SDL_windowsmodes.c", "sourceGroupIndex": 0}, {"backtrace": 52, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/windows/SDL_windowsmouse.c", "sourceGroupIndex": 0}, {"backtrace": 52, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/windows/SDL_windowsopengl.c", "sourceGroupIndex": 0}, {"backtrace": 52, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/windows/SDL_windowsopengles.c", "sourceGroupIndex": 0}, {"backtrace": 52, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/windows/SDL_windowsrawinput.c", "sourceGroupIndex": 0}, {"backtrace": 52, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/windows/SDL_windowsshape.c", "sourceGroupIndex": 0}, {"backtrace": 52, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/windows/SDL_windowsvideo.c", "sourceGroupIndex": 0}, {"backtrace": 52, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/windows/SDL_windowsvulkan.c", "sourceGroupIndex": 0}, {"backtrace": 52, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/windows/SDL_windowswindow.c", "sourceGroupIndex": 0}, {"backtrace": 54, "compileGroupIndex": 5, "path": "libs/SDL3/src/video/windows/SDL_windowsgameinput.cpp", "sourceGroupIndex": 0}, {"backtrace": 56, "compileGroupIndex": 2, "path": "libs/SDL3/src/thread/generic/SDL_syscond.c", "sourceGroupIndex": 0}, {"backtrace": 56, "compileGroupIndex": 2, "path": "libs/SDL3/src/thread/generic/SDL_sysrwlock.c", "sourceGroupIndex": 0}, {"backtrace": 56, "compileGroupIndex": 2, "path": "libs/SDL3/src/thread/windows/SDL_syscond_cv.c", "sourceGroupIndex": 0}, {"backtrace": 56, "compileGroupIndex": 2, "path": "libs/SDL3/src/thread/windows/SDL_sysmutex.c", "sourceGroupIndex": 0}, {"backtrace": 56, "compileGroupIndex": 2, "path": "libs/SDL3/src/thread/windows/SDL_sysrwlock_srw.c", "sourceGroupIndex": 0}, {"backtrace": 56, "compileGroupIndex": 2, "path": "libs/SDL3/src/thread/windows/SDL_syssem.c", "sourceGroupIndex": 0}, {"backtrace": 56, "compileGroupIndex": 2, "path": "libs/SDL3/src/thread/windows/SDL_systhread.c", "sourceGroupIndex": 0}, {"backtrace": 56, "compileGroupIndex": 2, "path": "libs/SDL3/src/thread/windows/SDL_systls.c", "sourceGroupIndex": 0}, {"backtrace": 58, "compileGroupIndex": 2, "path": "libs/SDL3/src/sensor/windows/SDL_windowssensor.c", "sourceGroupIndex": 0}, {"backtrace": 60, "compileGroupIndex": 2, "path": "libs/SDL3/src/power/windows/SDL_syspower.c", "sourceGroupIndex": 0}, {"backtrace": 62, "compileGroupIndex": 2, "path": "libs/SDL3/src/locale/windows/SDL_syslocale.c", "sourceGroupIndex": 0}, {"backtrace": 64, "compileGroupIndex": 2, "path": "libs/SDL3/src/filesystem/windows/SDL_sysfilesystem.c", "sourceGroupIndex": 0}, {"backtrace": 64, "compileGroupIndex": 2, "path": "libs/SDL3/src/filesystem/windows/SDL_sysfsops.c", "sourceGroupIndex": 0}, {"backtrace": 66, "compileGroupIndex": 2, "path": "libs/SDL3/src/storage/generic/SDL_genericstorage.c", "sourceGroupIndex": 0}, {"backtrace": 68, "compileGroupIndex": 2, "path": "libs/SDL3/src/storage/steam/SDL_steamstorage.c", "sourceGroupIndex": 0}, {"backtrace": 70, "compileGroupIndex": 2, "path": "libs/SDL3/src/time/windows/SDL_systime.c", "sourceGroupIndex": 0}, {"backtrace": 72, "compileGroupIndex": 2, "path": "libs/SDL3/src/timer/windows/SDL_systimer.c", "sourceGroupIndex": 0}, {"backtrace": 74, "compileGroupIndex": 2, "path": "libs/SDL3/src/loadso/windows/SDL_sysloadso.c", "sourceGroupIndex": 0}, {"backtrace": 76, "compileGroupIndex": 2, "path": "libs/SDL3/src/tray/windows/SDL_tray.c", "sourceGroupIndex": 0}, {"backtrace": 79, "compileGroupIndex": 2, "path": "libs/SDL3/src/joystick/hidapi/SDL_hidapi_8bitdo.c", "sourceGroupIndex": 0}, {"backtrace": 79, "compileGroupIndex": 2, "path": "libs/SDL3/src/joystick/hidapi/SDL_hidapi_combined.c", "sourceGroupIndex": 0}, {"backtrace": 79, "compileGroupIndex": 2, "path": "libs/SDL3/src/joystick/hidapi/SDL_hidapi_flydigi.c", "sourceGroupIndex": 0}, {"backtrace": 79, "compileGroupIndex": 2, "path": "libs/SDL3/src/joystick/hidapi/SDL_hidapi_gamecube.c", "sourceGroupIndex": 0}, {"backtrace": 79, "compileGroupIndex": 2, "path": "libs/SDL3/src/joystick/hidapi/SDL_hidapi_gip.c", "sourceGroupIndex": 0}, {"backtrace": 79, "compileGroupIndex": 2, "path": "libs/SDL3/src/joystick/hidapi/SDL_hidapi_lg4ff.c", "sourceGroupIndex": 0}, {"backtrace": 79, "compileGroupIndex": 2, "path": "libs/SDL3/src/joystick/hidapi/SDL_hidapi_luna.c", "sourceGroupIndex": 0}, {"backtrace": 79, "compileGroupIndex": 2, "path": "libs/SDL3/src/joystick/hidapi/SDL_hidapi_ps3.c", "sourceGroupIndex": 0}, {"backtrace": 79, "compileGroupIndex": 2, "path": "libs/SDL3/src/joystick/hidapi/SDL_hidapi_ps4.c", "sourceGroupIndex": 0}, {"backtrace": 79, "compileGroupIndex": 2, "path": "libs/SDL3/src/joystick/hidapi/SDL_hidapi_ps5.c", "sourceGroupIndex": 0}, {"backtrace": 79, "compileGroupIndex": 2, "path": "libs/SDL3/src/joystick/hidapi/SDL_hidapi_rumble.c", "sourceGroupIndex": 0}, {"backtrace": 79, "compileGroupIndex": 2, "path": "libs/SDL3/src/joystick/hidapi/SDL_hidapi_shield.c", "sourceGroupIndex": 0}, {"backtrace": 79, "compileGroupIndex": 2, "path": "libs/SDL3/src/joystick/hidapi/SDL_hidapi_sinput.c", "sourceGroupIndex": 0}, {"backtrace": 79, "compileGroupIndex": 2, "path": "libs/SDL3/src/joystick/hidapi/SDL_hidapi_stadia.c", "sourceGroupIndex": 0}, {"backtrace": 79, "compileGroupIndex": 2, "path": "libs/SDL3/src/joystick/hidapi/SDL_hidapi_steam.c", "sourceGroupIndex": 0}, {"backtrace": 79, "compileGroupIndex": 2, "path": "libs/SDL3/src/joystick/hidapi/SDL_hidapi_steam_hori.c", "sourceGroupIndex": 0}, {"backtrace": 79, "compileGroupIndex": 2, "path": "libs/SDL3/src/joystick/hidapi/SDL_hidapi_steamdeck.c", "sourceGroupIndex": 0}, {"backtrace": 79, "compileGroupIndex": 2, "path": "libs/SDL3/src/joystick/hidapi/SDL_hidapi_switch.c", "sourceGroupIndex": 0}, {"backtrace": 79, "compileGroupIndex": 2, "path": "libs/SDL3/src/joystick/hidapi/SDL_hidapi_wii.c", "sourceGroupIndex": 0}, {"backtrace": 79, "compileGroupIndex": 2, "path": "libs/SDL3/src/joystick/hidapi/SDL_hidapi_xbox360.c", "sourceGroupIndex": 0}, {"backtrace": 79, "compileGroupIndex": 2, "path": "libs/SDL3/src/joystick/hidapi/SDL_hidapi_xbox360w.c", "sourceGroupIndex": 0}, {"backtrace": 79, "compileGroupIndex": 2, "path": "libs/SDL3/src/joystick/hidapi/SDL_hidapi_xboxone.c", "sourceGroupIndex": 0}, {"backtrace": 79, "compileGroupIndex": 2, "path": "libs/SDL3/src/joystick/hidapi/SDL_hidapijoystick.c", "sourceGroupIndex": 0}, {"backtrace": 81, "compileGroupIndex": 2, "path": "libs/SDL3/src/haptic/hidapi/SDL_hidapihaptic.c", "sourceGroupIndex": 0}, {"backtrace": 81, "compileGroupIndex": 2, "path": "libs/SDL3/src/haptic/hidapi/SDL_hidapihaptic_lg4ff.c", "sourceGroupIndex": 0}, {"backtrace": 83, "compileGroupIndex": 2, "path": "libs/SDL3/src/joystick/windows/SDL_dinputjoystick.c", "sourceGroupIndex": 0}, {"backtrace": 83, "compileGroupIndex": 2, "path": "libs/SDL3/src/joystick/windows/SDL_rawinputjoystick.c", "sourceGroupIndex": 0}, {"backtrace": 83, "compileGroupIndex": 2, "path": "libs/SDL3/src/joystick/windows/SDL_windows_gaming_input.c", "sourceGroupIndex": 0}, {"backtrace": 83, "compileGroupIndex": 2, "path": "libs/SDL3/src/joystick/windows/SDL_windowsjoystick.c", "sourceGroupIndex": 0}, {"backtrace": 83, "compileGroupIndex": 2, "path": "libs/SDL3/src/joystick/windows/SDL_xinputjoystick.c", "sourceGroupIndex": 0}, {"backtrace": 85, "compileGroupIndex": 2, "path": "libs/SDL3/src/haptic/windows/SDL_dinputhaptic.c", "sourceGroupIndex": 0}, {"backtrace": 85, "compileGroupIndex": 2, "path": "libs/SDL3/src/haptic/windows/SDL_windowshaptic.c", "sourceGroupIndex": 0}, {"backtrace": 87, "compileGroupIndex": 2, "path": "libs/SDL3/src/camera/mediafoundation/SDL_camera_mediafoundation.c", "sourceGroupIndex": 0}, {"backtrace": 89, "compileGroupIndex": 6, "path": "libs/SDL3/src/core/windows/version.rc", "sourceGroupIndex": 0}, {"backtrace": 91, "compileGroupIndex": 2, "path": "libs/SDL3/src/dialog/SDL_dialog.c", "sourceGroupIndex": 0}, {"backtrace": 93, "compileGroupIndex": 2, "path": "libs/SDL3/src/dialog/SDL_dialog_utils.c", "sourceGroupIndex": 0}, {"backtrace": 95, "compileGroupIndex": 2, "path": "libs/SDL3/src/dialog/windows/SDL_windowsdialog.c", "sourceGroupIndex": 0}, {"backtrace": 97, "compileGroupIndex": 2, "path": "libs/SDL3/src/process/SDL_process.c", "sourceGroupIndex": 0}, {"backtrace": 99, "compileGroupIndex": 2, "path": "libs/SDL3/src/process/windows/SDL_windowsprocess.c", "sourceGroupIndex": 0}, {"backtrace": 101, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/offscreen/SDL_offscreenevents.c", "sourceGroupIndex": 0}, {"backtrace": 101, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/offscreen/SDL_offscreenframebuffer.c", "sourceGroupIndex": 0}, {"backtrace": 101, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/offscreen/SDL_offscreenopengles.c", "sourceGroupIndex": 0}, {"backtrace": 101, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/offscreen/SDL_offscreenvideo.c", "sourceGroupIndex": 0}, {"backtrace": 101, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/offscreen/SDL_offscreenvulkan.c", "sourceGroupIndex": 0}, {"backtrace": 101, "compileGroupIndex": 2, "path": "libs/SDL3/src/video/offscreen/SDL_offscreenwindow.c", "sourceGroupIndex": 0}, {"backtrace": 103, "compileGroupIndex": 2, "path": "libs/SDL3/src/tray/SDL_tray_utils.c", "sourceGroupIndex": 0}, {"backtrace": 105, "compileGroupIndex": 2, "path": "libs/SDL3/src/gpu/d3d12/SDL_gpu_d3d12.c", "sourceGroupIndex": 0}, {"backtrace": 107, "compileGroupIndex": 2, "path": "libs/SDL3/src/gpu/vulkan/SDL_gpu_vulkan.c", "sourceGroupIndex": 0}, {"backtrace": 109, "compileGroupIndex": 2, "path": "libs/SDL3/src/main/generic/SDL_sysmain_callbacks.c", "sourceGroupIndex": 0}, {"backtrace": 0, "path": "build/libs/SDL3/CMakeFiles/SDL3-shared.dir/Debug/cmake_pch.h", "sourceGroupIndex": 1}, {"backtrace": 0, "path": "build/libs/SDL3/CMakeFiles/SDL3-shared.dir/Debug/cmake_pch.hxx", "sourceGroupIndex": 1}, {"backtrace": 0, "path": "build/libs/SDL3/CMakeFiles/SDL3-shared.dir/Release/cmake_pch.h", "sourceGroupIndex": 1}, {"backtrace": 0, "path": "build/libs/SDL3/CMakeFiles/SDL3-shared.dir/Release/cmake_pch.hxx", "sourceGroupIndex": 1}, {"backtrace": 0, "path": "build/libs/SDL3/CMakeFiles/SDL3-shared.dir/MinSizeRel/cmake_pch.h", "sourceGroupIndex": 1}, {"backtrace": 0, "path": "build/libs/SDL3/CMakeFiles/SDL3-shared.dir/MinSizeRel/cmake_pch.hxx", "sourceGroupIndex": 1}, {"backtrace": 0, "path": "build/libs/SDL3/CMakeFiles/SDL3-shared.dir/RelWithDebInfo/cmake_pch.h", "sourceGroupIndex": 1}, {"backtrace": 0, "path": "build/libs/SDL3/CMakeFiles/SDL3-shared.dir/RelWithDebInfo/cmake_pch.hxx", "sourceGroupIndex": 1}], "type": "SHARED_LIBRARY"}