﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{B0B431C4-DB32-3B1C-A4EF-62B60D481F0A}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>SDL3_test</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\Frank Work All\CPP\f0\build\libs\SDL3\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">SDL3_test.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">SDL3_test</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\Frank Work All\CPP\f0\build\libs\SDL3\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">SDL3_test.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">SDL3_test</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\Frank Work All\CPP\f0\build\libs\SDL3\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">SDL3_test.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">SDL3_test</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\Frank Work All\CPP\f0\build\libs\SDL3\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">SDL3_test.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">SDL3_test</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Frank Work All\CPP\f0\build\libs\SDL3\include-revision;D:\Frank Work All\CPP\f0\libs\SDL3\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>
      </ExceptionHandling>
      <ForceConformanceInForLoopScope></ForceConformanceInForLoopScope>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RemoveUnreferencedCodeData></RemoveUnreferencedCodeData>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWChar_tAsBuiltInType></TreatWChar_tAsBuiltInType>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Frank Work All\CPP\f0\build\libs\SDL3\include-revision;D:\Frank Work All\CPP\f0\libs\SDL3\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Frank Work All\CPP\f0\build\libs\SDL3\include-revision;D:\Frank Work All\CPP\f0\libs\SDL3\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Frank Work All\CPP\f0\build\libs\SDL3\include-revision;D:\Frank Work All\CPP\f0\libs\SDL3\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>
      </ExceptionHandling>
      <ForceConformanceInForLoopScope></ForceConformanceInForLoopScope>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RemoveUnreferencedCodeData></RemoveUnreferencedCodeData>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWChar_tAsBuiltInType></TreatWChar_tAsBuiltInType>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Frank Work All\CPP\f0\build\libs\SDL3\include-revision;D:\Frank Work All\CPP\f0\libs\SDL3\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Frank Work All\CPP\f0\build\libs\SDL3\include-revision;D:\Frank Work All\CPP\f0\libs\SDL3\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Frank Work All\CPP\f0\build\libs\SDL3\include-revision;D:\Frank Work All\CPP\f0\libs\SDL3\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>
      </ExceptionHandling>
      <ForceConformanceInForLoopScope></ForceConformanceInForLoopScope>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RemoveUnreferencedCodeData></RemoveUnreferencedCodeData>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWChar_tAsBuiltInType></TreatWChar_tAsBuiltInType>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Frank Work All\CPP\f0\build\libs\SDL3\include-revision;D:\Frank Work All\CPP\f0\libs\SDL3\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Frank Work All\CPP\f0\build\libs\SDL3\include-revision;D:\Frank Work All\CPP\f0\libs\SDL3\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Frank Work All\CPP\f0\build\libs\SDL3\include-revision;D:\Frank Work All\CPP\f0\libs\SDL3\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>
      </ExceptionHandling>
      <ForceConformanceInForLoopScope></ForceConformanceInForLoopScope>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RemoveUnreferencedCodeData></RemoveUnreferencedCodeData>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWChar_tAsBuiltInType></TreatWChar_tAsBuiltInType>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Frank Work All\CPP\f0\build\libs\SDL3\include-revision;D:\Frank Work All\CPP\f0\libs\SDL3\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Frank Work All\CPP\f0\build\libs\SDL3\include-revision;D:\Frank Work All\CPP\f0\libs\SDL3\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\Frank Work All\CPP\f0\libs\SDL3\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/Frank Work All/CPP/f0/libs/SDL3/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" "-SD:/Frank Work All/CPP/f0" "-BD:/Frank Work All/CPP/f0/build" --check-stamp-file "D:/Frank Work All/CPP/f0/build/libs/SDL3/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\CMake\share\cmake-4.1\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeParseArguments.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakePushCheckState.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCSourceRuns.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckLanguage.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckLinkerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckStructHasMember.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckSymbolExists.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPkgConfig.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckLinkerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckSourceRuns.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\WriteBasicConfigVersionFile.cmake;D:\Frank Work All\CPP\f0\build\libs\SDL3\CMakeFiles\SDL_build_config.h.intermediate;D:\Frank Work All\CPP\f0\build\libs\SDL3\CMakeFiles\git-data\grabRef.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\.git\HEAD;D:\Frank Work All\CPP\f0\libs\SDL3\.git\refs\heads\main;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\3rdparty.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\FindLibUSB.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\GetGitRevisionDescription.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\GetGitRevisionDescription.cmake.in;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\PreseedEmscriptenCache.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\PreseedMSVCCache.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\PreseedNokiaNGageCache.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\SDL3Config.cmake.in;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\macros.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\sdlchecks.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\sdlcommands.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\sdlcompilers.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\sdlcpu.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\sdlmanpages.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\sdlplatform.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\include\build_config\SDL_build_config.h.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\include\build_config\SDL_revision.h.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\Frank Work All\CPP\f0\build\libs\SDL3\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/Frank Work All/CPP/f0/libs/SDL3/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" "-SD:/Frank Work All/CPP/f0" "-BD:/Frank Work All/CPP/f0/build" --check-stamp-file "D:/Frank Work All/CPP/f0/build/libs/SDL3/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\CMake\share\cmake-4.1\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeParseArguments.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakePushCheckState.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCSourceRuns.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckLanguage.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckLinkerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckStructHasMember.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckSymbolExists.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPkgConfig.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckLinkerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckSourceRuns.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\WriteBasicConfigVersionFile.cmake;D:\Frank Work All\CPP\f0\build\libs\SDL3\CMakeFiles\SDL_build_config.h.intermediate;D:\Frank Work All\CPP\f0\build\libs\SDL3\CMakeFiles\git-data\grabRef.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\.git\HEAD;D:\Frank Work All\CPP\f0\libs\SDL3\.git\refs\heads\main;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\3rdparty.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\FindLibUSB.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\GetGitRevisionDescription.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\GetGitRevisionDescription.cmake.in;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\PreseedEmscriptenCache.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\PreseedMSVCCache.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\PreseedNokiaNGageCache.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\SDL3Config.cmake.in;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\macros.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\sdlchecks.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\sdlcommands.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\sdlcompilers.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\sdlcpu.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\sdlmanpages.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\sdlplatform.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\include\build_config\SDL_build_config.h.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\include\build_config\SDL_revision.h.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\Frank Work All\CPP\f0\build\libs\SDL3\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/Frank Work All/CPP/f0/libs/SDL3/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" "-SD:/Frank Work All/CPP/f0" "-BD:/Frank Work All/CPP/f0/build" --check-stamp-file "D:/Frank Work All/CPP/f0/build/libs/SDL3/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\CMake\share\cmake-4.1\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeParseArguments.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakePushCheckState.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCSourceRuns.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckLanguage.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckLinkerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckStructHasMember.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckSymbolExists.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPkgConfig.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckLinkerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckSourceRuns.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\WriteBasicConfigVersionFile.cmake;D:\Frank Work All\CPP\f0\build\libs\SDL3\CMakeFiles\SDL_build_config.h.intermediate;D:\Frank Work All\CPP\f0\build\libs\SDL3\CMakeFiles\git-data\grabRef.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\.git\HEAD;D:\Frank Work All\CPP\f0\libs\SDL3\.git\refs\heads\main;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\3rdparty.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\FindLibUSB.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\GetGitRevisionDescription.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\GetGitRevisionDescription.cmake.in;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\PreseedEmscriptenCache.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\PreseedMSVCCache.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\PreseedNokiaNGageCache.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\SDL3Config.cmake.in;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\macros.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\sdlchecks.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\sdlcommands.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\sdlcompilers.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\sdlcpu.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\sdlmanpages.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\sdlplatform.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\include\build_config\SDL_build_config.h.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\include\build_config\SDL_revision.h.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\Frank Work All\CPP\f0\build\libs\SDL3\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/Frank Work All/CPP/f0/libs/SDL3/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" "-SD:/Frank Work All/CPP/f0" "-BD:/Frank Work All/CPP/f0/build" --check-stamp-file "D:/Frank Work All/CPP/f0/build/libs/SDL3/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\CMake\share\cmake-4.1\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeParseArguments.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakePushCheckState.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCSourceRuns.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckLanguage.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckLinkerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckStructHasMember.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckSymbolExists.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPkgConfig.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckLinkerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckSourceRuns.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\WriteBasicConfigVersionFile.cmake;D:\Frank Work All\CPP\f0\build\libs\SDL3\CMakeFiles\SDL_build_config.h.intermediate;D:\Frank Work All\CPP\f0\build\libs\SDL3\CMakeFiles\git-data\grabRef.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\.git\HEAD;D:\Frank Work All\CPP\f0\libs\SDL3\.git\refs\heads\main;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\3rdparty.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\FindLibUSB.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\GetGitRevisionDescription.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\GetGitRevisionDescription.cmake.in;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\PreseedEmscriptenCache.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\PreseedMSVCCache.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\PreseedNokiaNGageCache.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\SDL3Config.cmake.in;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\macros.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\sdlchecks.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\sdlcommands.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\sdlcompilers.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\sdlcpu.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\sdlmanpages.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\cmake\sdlplatform.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\include\build_config\SDL_build_config.h.cmake;D:\Frank Work All\CPP\f0\libs\SDL3\include\build_config\SDL_revision.h.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\Frank Work All\CPP\f0\build\libs\SDL3\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\test\SDL_test_assert.c" />
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\test\SDL_test_common.c" />
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\test\SDL_test_compare.c" />
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\test\SDL_test_crc32.c" />
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\test\SDL_test_font.c" />
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\test\SDL_test_fuzzer.c" />
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\test\SDL_test_harness.c" />
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\test\SDL_test_log.c" />
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\test\SDL_test_md5.c" />
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\test\SDL_test_memory.c" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\Frank Work All\CPP\f0\build\ZERO_CHECK.vcxproj">
      <Project>{635B9BB6-FF4C-3E90-A489-8C8EC307D9C2}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>