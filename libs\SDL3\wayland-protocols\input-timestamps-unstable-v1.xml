<?xml version="1.0" encoding="UTF-8"?>
<protocol name="input_timestamps_unstable_v1">

  <copyright>
    Copyright © 2017 Collabora, Ltd.

    Permission is hereby granted, free of charge, to any person obtaining a
    copy of this software and associated documentation files (the "Software"),
    to deal in the Software without restriction, including without limitation
    the rights to use, copy, modify, merge, publish, distribute, sublicense,
    and/or sell copies of the Software, and to permit persons to whom the
    Software is furnished to do so, subject to the following conditions:

    The above copyright notice and this permission notice (including the next
    paragraph) shall be included in all copies or substantial portions of the
    Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
    THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
    FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
    DEALINGS IN THE SOFTWARE.
  </copyright>

  <description summary="High-resolution timestamps for input events">
    This protocol specifies a way for a client to request and receive
    high-resolution timestamps for input events.

    Warning! The protocol described in this file is experimental and
    backward incompatible changes may be made. Backward compatible changes
    may be added together with the corresponding interface version bump.
    Backward incompatible changes are done by bumping the version number in
    the protocol and interface names and resetting the interface version.
    Once the protocol is to be declared stable, the 'z' prefix and the
    version number in the protocol and interface names are removed and the
    interface version number is reset.
  </description>

  <interface name="zwp_input_timestamps_manager_v1" version="1">
    <description summary="context object for high-resolution input timestamps">
      A global interface used for requesting high-resolution timestamps
      for input events.
    </description>

    <request name="destroy" type="destructor">
      <description summary="destroy the input timestamps manager object">
        Informs the server that the client will no longer be using this
        protocol object. Existing objects created by this object are not
        affected.
      </description>
    </request>

    <request name="get_keyboard_timestamps">
      <description summary="subscribe to high-resolution keyboard timestamp events">
        Creates a new input timestamps object that represents a subscription
        to high-resolution timestamp events for all wl_keyboard events that
        carry a timestamp.

        If the associated wl_keyboard object is invalidated, either through
        client action (e.g. release) or server-side changes, the input
        timestamps object becomes inert and the client should destroy it
        by calling zwp_input_timestamps_v1.destroy.
      </description>
      <arg name="id" type="new_id" interface="zwp_input_timestamps_v1"/>
      <arg name="keyboard" type="object" interface="wl_keyboard"
           summary="the wl_keyboard object for which to get timestamp events"/>
    </request>

    <request name="get_pointer_timestamps">
      <description summary="subscribe to high-resolution pointer timestamp events">
        Creates a new input timestamps object that represents a subscription
        to high-resolution timestamp events for all wl_pointer events that
        carry a timestamp.

        If the associated wl_pointer object is invalidated, either through
        client action (e.g. release) or server-side changes, the input
        timestamps object becomes inert and the client should destroy it
        by calling zwp_input_timestamps_v1.destroy.
      </description>
      <arg name="id" type="new_id" interface="zwp_input_timestamps_v1"/>
      <arg name="pointer" type="object" interface="wl_pointer"
           summary="the wl_pointer object for which to get timestamp events"/>
    </request>

    <request name="get_touch_timestamps">
      <description summary="subscribe to high-resolution touch timestamp events">
        Creates a new input timestamps object that represents a subscription
        to high-resolution timestamp events for all wl_touch events that
        carry a timestamp.

        If the associated wl_touch object becomes invalid, either through
        client action (e.g. release) or server-side changes, the input
        timestamps object becomes inert and the client should destroy it
        by calling zwp_input_timestamps_v1.destroy.
      </description>
      <arg name="id" type="new_id" interface="zwp_input_timestamps_v1"/>
      <arg name="touch" type="object" interface="wl_touch"
           summary="the wl_touch object for which to get timestamp events"/>
    </request>
  </interface>

  <interface name="zwp_input_timestamps_v1" version="1">
    <description summary="context object for input timestamps">
      Provides high-resolution timestamp events for a set of subscribed input
      events. The set of subscribed input events is determined by the
      zwp_input_timestamps_manager_v1 request used to create this object.
    </description>

    <request name="destroy" type="destructor">
      <description summary="destroy the input timestamps object">
        Informs the server that the client will no longer be using this
        protocol object. After the server processes the request, no more
        timestamp events will be emitted.
      </description>
    </request>

    <event name="timestamp">
      <description summary="high-resolution timestamp event">
        The timestamp event is associated with the first subsequent input event
        carrying a timestamp which belongs to the set of input events this
        object is subscribed to.

        The timestamp provided by this event is a high-resolution version of
        the timestamp argument of the associated input event. The provided
        timestamp is in the same clock domain and is at least as accurate as
        the associated input event timestamp.

        The timestamp is expressed as tv_sec_hi, tv_sec_lo, tv_nsec triples,
        each component being an unsigned 32-bit value. Whole seconds are in
        tv_sec which is a 64-bit value combined from tv_sec_hi and tv_sec_lo,
        and the additional fractional part in tv_nsec as nanoseconds. Hence,
        for valid timestamps tv_nsec must be in [0, 999999999].
      </description>
      <arg name="tv_sec_hi" type="uint"
           summary="high 32 bits of the seconds part of the timestamp"/>
      <arg name="tv_sec_lo" type="uint"
           summary="low 32 bits of the seconds part of the timestamp"/>
      <arg name="tv_nsec" type="uint"
           summary="nanoseconds part of the timestamp"/>
    </event>
  </interface>

</protocol>
