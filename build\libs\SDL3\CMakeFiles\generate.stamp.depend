# CMake generation dependency list for this directory.
C:/Program Files/CMake/share/cmake-4.1/Modules/BasicConfigVersion-AnyNewerVersion.cmake.in
C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDependentOption.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CMakePackageConfigHelpers.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeParseArguments.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CMakePushCheckState.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CheckCCompilerFlag.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CheckCSourceCompiles.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CheckCSourceRuns.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CheckCXXCompilerFlag.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CheckCXXSourceCompiles.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CheckIncludeFile.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CheckLanguage.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CheckLibraryExists.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CheckLinkerFlag.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CheckSourceCompiles.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CheckStructHasMember.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/CheckSymbolExists.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/FindGit.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/FindPackageHandleStandardArgs.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/FindPackageMessage.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/FindPkgConfig.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/GNUInstallDirs.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CheckCompilerFlag.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CheckFlagCommonConfig.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CheckLinkerFlag.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CheckSourceCompiles.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CheckSourceRuns.cmake
C:/Program Files/CMake/share/cmake-4.1/Modules/WriteBasicConfigVersionFile.cmake
D:/Frank Work All/CPP/f0/build/libs/SDL3/CMakeFiles/SDL_build_config.h.intermediate
D:/Frank Work All/CPP/f0/build/libs/SDL3/CMakeFiles/git-data/grabRef.cmake
D:/Frank Work All/CPP/f0/libs/SDL3/.git/HEAD
D:/Frank Work All/CPP/f0/libs/SDL3/.git/refs/heads/main
D:/Frank Work All/CPP/f0/libs/SDL3/CMakeLists.txt
D:/Frank Work All/CPP/f0/libs/SDL3/cmake/3rdparty.cmake
D:/Frank Work All/CPP/f0/libs/SDL3/cmake/FindLibUSB.cmake
D:/Frank Work All/CPP/f0/libs/SDL3/cmake/GetGitRevisionDescription.cmake
D:/Frank Work All/CPP/f0/libs/SDL3/cmake/GetGitRevisionDescription.cmake.in
D:/Frank Work All/CPP/f0/libs/SDL3/cmake/PreseedEmscriptenCache.cmake
D:/Frank Work All/CPP/f0/libs/SDL3/cmake/PreseedMSVCCache.cmake
D:/Frank Work All/CPP/f0/libs/SDL3/cmake/PreseedNokiaNGageCache.cmake
D:/Frank Work All/CPP/f0/libs/SDL3/cmake/SDL3Config.cmake.in
D:/Frank Work All/CPP/f0/libs/SDL3/cmake/macros.cmake
D:/Frank Work All/CPP/f0/libs/SDL3/cmake/sdlchecks.cmake
D:/Frank Work All/CPP/f0/libs/SDL3/cmake/sdlcommands.cmake
D:/Frank Work All/CPP/f0/libs/SDL3/cmake/sdlcompilers.cmake
D:/Frank Work All/CPP/f0/libs/SDL3/cmake/sdlcpu.cmake
D:/Frank Work All/CPP/f0/libs/SDL3/cmake/sdlmanpages.cmake
D:/Frank Work All/CPP/f0/libs/SDL3/cmake/sdlplatform.cmake
D:/Frank Work All/CPP/f0/libs/SDL3/include/build_config/SDL_build_config.h.cmake
D:/Frank Work All/CPP/f0/libs/SDL3/include/build_config/SDL_revision.h.cmake
