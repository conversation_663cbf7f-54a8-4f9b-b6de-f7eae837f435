
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineSystem.cmake:212 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Users\\<USER>\\scoop\\shims"
        - "C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ProgramData\\mingw64\\mingw64\\bin"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.13.19+0d9f5a35a for .NET Framework
      Build started 8/11/2025 12:42:40 AM.
      
      Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\4.1.0\\CompilerIdC\\CompilerIdC.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdC.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdC.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\4.1.0\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate".
      Done Building Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\4.1.0\\CompilerIdC\\CompilerIdC.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:00.79
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        D:/Frank Work All/CPP/f0/build/CMakeFiles/4.1.0/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:103 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/"
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Users/<USER>/scoop/shims/"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ProgramData/mingw64/mingw64/bin/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/link.com"
    found: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/link.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Users\\<USER>\\scoop\\shims"
        - "C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ProgramData\\mingw64\\mingw64\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:104 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lld-link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/"
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Users/<USER>/scoop/shims/"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ProgramData/mingw64/mingw64/bin/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lld-link"
      - "C:/Program Files/Oculus/Support/oculus-runtime/lld-link.com"
      - "C:/Program Files/Oculus/Support/oculus-runtime/lld-link.exe"
      - "C:/Program Files/Oculus/Support/oculus-runtime/lld-link"
      - "C:/Windows/System32/lld-link.com"
      - "C:/Windows/System32/lld-link.exe"
      - "C:/Windows/System32/lld-link"
      - "C:/Windows/lld-link.com"
      - "C:/Windows/lld-link.exe"
      - "C:/Windows/lld-link"
      - "C:/Windows/System32/wbem/lld-link.com"
      - "C:/Windows/System32/wbem/lld-link.exe"
      - "C:/Windows/System32/wbem/lld-link"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link"
      - "C:/Windows/System32/OpenSSH/lld-link.com"
      - "C:/Windows/System32/OpenSSH/lld-link.exe"
      - "C:/Windows/System32/OpenSSH/lld-link"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/lld-link.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/lld-link.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/lld-link"
      - "C:/Program Files/dotnet/lld-link.com"
      - "C:/Program Files/dotnet/lld-link.exe"
      - "C:/Program Files/dotnet/lld-link"
      - "C:/Program Files/Git/cmd/lld-link.com"
      - "C:/Program Files/Git/cmd/lld-link.exe"
      - "C:/Program Files/Git/cmd/lld-link"
      - "C:/Program Files/nodejs/lld-link.com"
      - "C:/Program Files/nodejs/lld-link.exe"
      - "C:/Program Files/nodejs/lld-link"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/lld-link.com"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/lld-link.exe"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/lld-link"
      - "C:/Program Files/Docker/Docker/resources/bin/lld-link.com"
      - "C:/Program Files/Docker/Docker/resources/bin/lld-link.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/lld-link"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link"
      - "C:/Program Files/CMake/bin/lld-link.com"
      - "C:/Program Files/CMake/bin/lld-link.exe"
      - "C:/Program Files/CMake/bin/lld-link"
      - "C:/ProgramData/chocolatey/bin/lld-link.com"
      - "C:/ProgramData/chocolatey/bin/lld-link.exe"
      - "C:/ProgramData/chocolatey/bin/lld-link"
      - "C:/Users/<USER>/scoop/shims/lld-link.com"
      - "C:/Users/<USER>/scoop/shims/lld-link.exe"
      - "C:/Users/<USER>/scoop/shims/lld-link"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/lld-link.com"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/lld-link.exe"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link"
      - "C:/Users/<USER>/.dotnet/tools/lld-link.com"
      - "C:/Users/<USER>/.dotnet/tools/lld-link.exe"
      - "C:/Users/<USER>/.dotnet/tools/lld-link"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link"
      - "C:/ProgramData/mingw64/mingw64/bin/lld-link.com"
      - "C:/ProgramData/mingw64/mingw64/bin/lld-link.exe"
      - "C:/ProgramData/mingw64/mingw64/bin/lld-link"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Users\\<USER>\\scoop\\shims"
        - "C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ProgramData\\mingw64\\mingw64\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_LINKER"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/"
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Users/<USER>/scoop/shims/"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ProgramData/mingw64/mingw64/bin/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/link.com"
    found: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/link.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Users\\<USER>\\scoop\\shims"
        - "C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ProgramData\\mingw64\\mingw64\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_MT"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "mt"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/"
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Users/<USER>/scoop/shims/"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ProgramData/mingw64/mingw64/bin/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/mt.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/mt.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/mt"
      - "C:/Program Files/Oculus/Support/oculus-runtime/mt.com"
      - "C:/Program Files/Oculus/Support/oculus-runtime/mt.exe"
      - "C:/Program Files/Oculus/Support/oculus-runtime/mt"
      - "C:/Windows/System32/mt.com"
      - "C:/Windows/System32/mt.exe"
      - "C:/Windows/System32/mt"
      - "C:/Windows/mt.com"
      - "C:/Windows/mt.exe"
      - "C:/Windows/mt"
      - "C:/Windows/System32/wbem/mt.com"
      - "C:/Windows/System32/wbem/mt.exe"
      - "C:/Windows/System32/wbem/mt"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt"
      - "C:/Windows/System32/OpenSSH/mt.com"
      - "C:/Windows/System32/OpenSSH/mt.exe"
      - "C:/Windows/System32/OpenSSH/mt"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/mt.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/mt.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/mt"
      - "C:/Program Files/dotnet/mt.com"
      - "C:/Program Files/dotnet/mt.exe"
      - "C:/Program Files/dotnet/mt"
      - "C:/Program Files/Git/cmd/mt.com"
      - "C:/Program Files/Git/cmd/mt.exe"
      - "C:/Program Files/Git/cmd/mt"
      - "C:/Program Files/nodejs/mt.com"
      - "C:/Program Files/nodejs/mt.exe"
      - "C:/Program Files/nodejs/mt"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/mt.com"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/mt.exe"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/mt"
      - "C:/Program Files/Docker/Docker/resources/bin/mt.com"
      - "C:/Program Files/Docker/Docker/resources/bin/mt.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/mt"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/mt.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/mt.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/mt"
      - "C:/Program Files/CMake/bin/mt.com"
      - "C:/Program Files/CMake/bin/mt.exe"
      - "C:/Program Files/CMake/bin/mt"
      - "C:/ProgramData/chocolatey/bin/mt.com"
      - "C:/ProgramData/chocolatey/bin/mt.exe"
      - "C:/ProgramData/chocolatey/bin/mt"
      - "C:/Users/<USER>/scoop/shims/mt.com"
      - "C:/Users/<USER>/scoop/shims/mt.exe"
      - "C:/Users/<USER>/scoop/shims/mt"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/mt.com"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/mt.exe"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/mt"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt"
      - "C:/Users/<USER>/.dotnet/tools/mt.com"
      - "C:/Users/<USER>/.dotnet/tools/mt.exe"
      - "C:/Users/<USER>/.dotnet/tools/mt"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/mt.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/mt.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/mt"
      - "C:/Users/<USER>/AppData/Roaming/npm/mt.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/mt.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/mt"
      - "C:/ProgramData/mingw64/mingw64/bin/mt.com"
      - "C:/ProgramData/mingw64/mingw64/bin/mt.exe"
      - "C:/ProgramData/mingw64/mingw64/bin/mt"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Users\\<USER>\\scoop\\shims"
        - "C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ProgramData\\mingw64\\mingw64\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_AR"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lib"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/"
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Users/<USER>/scoop/shims/"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ProgramData/mingw64/mingw64/bin/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lib.com"
    found: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lib.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Users\\<USER>\\scoop\\shims"
        - "C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ProgramData\\mingw64\\mingw64\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Users\\<USER>\\scoop\\shims"
        - "C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ProgramData\\mingw64\\mingw64\\bin"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.13.19+0d9f5a35a for .NET Framework
      Build started 8/11/2025 12:42:40 AM.
      
      Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\4.1.0\\CompilerIdCXX\\CompilerIdCXX.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\4.1.0\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Done Building Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\4.1.0\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:00.73
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        D:/Frank Work All/CPP/f0/build/CMakeFiles/4.1.0/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:104 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lld-link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/"
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Users/<USER>/scoop/shims/"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ProgramData/mingw64/mingw64/bin/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lld-link"
      - "C:/Program Files/Oculus/Support/oculus-runtime/lld-link.com"
      - "C:/Program Files/Oculus/Support/oculus-runtime/lld-link.exe"
      - "C:/Program Files/Oculus/Support/oculus-runtime/lld-link"
      - "C:/Windows/System32/lld-link.com"
      - "C:/Windows/System32/lld-link.exe"
      - "C:/Windows/System32/lld-link"
      - "C:/Windows/lld-link.com"
      - "C:/Windows/lld-link.exe"
      - "C:/Windows/lld-link"
      - "C:/Windows/System32/wbem/lld-link.com"
      - "C:/Windows/System32/wbem/lld-link.exe"
      - "C:/Windows/System32/wbem/lld-link"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link"
      - "C:/Windows/System32/OpenSSH/lld-link.com"
      - "C:/Windows/System32/OpenSSH/lld-link.exe"
      - "C:/Windows/System32/OpenSSH/lld-link"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/lld-link.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/lld-link.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/lld-link"
      - "C:/Program Files/dotnet/lld-link.com"
      - "C:/Program Files/dotnet/lld-link.exe"
      - "C:/Program Files/dotnet/lld-link"
      - "C:/Program Files/Git/cmd/lld-link.com"
      - "C:/Program Files/Git/cmd/lld-link.exe"
      - "C:/Program Files/Git/cmd/lld-link"
      - "C:/Program Files/nodejs/lld-link.com"
      - "C:/Program Files/nodejs/lld-link.exe"
      - "C:/Program Files/nodejs/lld-link"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/lld-link.com"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/lld-link.exe"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/lld-link"
      - "C:/Program Files/Docker/Docker/resources/bin/lld-link.com"
      - "C:/Program Files/Docker/Docker/resources/bin/lld-link.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/lld-link"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link"
      - "C:/Program Files/CMake/bin/lld-link.com"
      - "C:/Program Files/CMake/bin/lld-link.exe"
      - "C:/Program Files/CMake/bin/lld-link"
      - "C:/ProgramData/chocolatey/bin/lld-link.com"
      - "C:/ProgramData/chocolatey/bin/lld-link.exe"
      - "C:/ProgramData/chocolatey/bin/lld-link"
      - "C:/Users/<USER>/scoop/shims/lld-link.com"
      - "C:/Users/<USER>/scoop/shims/lld-link.exe"
      - "C:/Users/<USER>/scoop/shims/lld-link"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/lld-link.com"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/lld-link.exe"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link"
      - "C:/Users/<USER>/.dotnet/tools/lld-link.com"
      - "C:/Users/<USER>/.dotnet/tools/lld-link.exe"
      - "C:/Users/<USER>/.dotnet/tools/lld-link"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link"
      - "C:/ProgramData/mingw64/mingw64/bin/lld-link.com"
      - "C:/ProgramData/mingw64/mingw64/bin/lld-link.exe"
      - "C:/ProgramData/mingw64/mingw64/bin/lld-link"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Users\\<USER>\\scoop\\shims"
        - "C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ProgramData\\mingw64\\mingw64\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineRCCompiler.cmake:40 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC.cmake:574 (enable_language)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC.cmake:547 (__windows_compiler_msvc_enable_rc)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC-C.cmake:5 (__windows_compiler_msvc)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCInformation.cmake:48 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_RC_COMPILER"
    description: "RC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "rc"
    candidate_directories:
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Users/<USER>/scoop/shims/"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ProgramData/mingw64/mingw64/bin/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/MySDL3Game/bin/"
      - "C:/Program Files (x86)/MySDL3Game/sbin/"
      - "C:/Program Files (x86)/MySDL3Game/"
    searched_directories:
      - "C:/Program Files/Oculus/Support/oculus-runtime/rc.com"
      - "C:/Program Files/Oculus/Support/oculus-runtime/rc.exe"
      - "C:/Program Files/Oculus/Support/oculus-runtime/rc"
      - "C:/Windows/System32/rc.com"
      - "C:/Windows/System32/rc.exe"
      - "C:/Windows/System32/rc"
      - "C:/Windows/rc.com"
      - "C:/Windows/rc.exe"
      - "C:/Windows/rc"
      - "C:/Windows/System32/wbem/rc.com"
      - "C:/Windows/System32/wbem/rc.exe"
      - "C:/Windows/System32/wbem/rc"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc"
      - "C:/Windows/System32/OpenSSH/rc.com"
      - "C:/Windows/System32/OpenSSH/rc.exe"
      - "C:/Windows/System32/OpenSSH/rc"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/rc.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/rc.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/rc"
      - "C:/Program Files/dotnet/rc.com"
      - "C:/Program Files/dotnet/rc.exe"
      - "C:/Program Files/dotnet/rc"
      - "C:/Program Files/Git/cmd/rc.com"
      - "C:/Program Files/Git/cmd/rc.exe"
      - "C:/Program Files/Git/cmd/rc"
      - "C:/Program Files/nodejs/rc.com"
      - "C:/Program Files/nodejs/rc.exe"
      - "C:/Program Files/nodejs/rc"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/rc.com"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/rc.exe"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/rc"
      - "C:/Program Files/Docker/Docker/resources/bin/rc.com"
      - "C:/Program Files/Docker/Docker/resources/bin/rc.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/rc"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/rc.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/rc.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/rc"
      - "C:/Program Files/CMake/bin/rc.com"
      - "C:/Program Files/CMake/bin/rc.exe"
      - "C:/Program Files/CMake/bin/rc"
      - "C:/ProgramData/chocolatey/bin/rc.com"
      - "C:/ProgramData/chocolatey/bin/rc.exe"
      - "C:/ProgramData/chocolatey/bin/rc"
      - "C:/Users/<USER>/scoop/shims/rc.com"
      - "C:/Users/<USER>/scoop/shims/rc.exe"
      - "C:/Users/<USER>/scoop/shims/rc"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/rc.com"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/rc.exe"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/rc"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc"
      - "C:/Users/<USER>/.dotnet/tools/rc.com"
      - "C:/Users/<USER>/.dotnet/tools/rc.exe"
      - "C:/Users/<USER>/.dotnet/tools/rc"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/rc.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/rc.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/rc"
      - "C:/Users/<USER>/AppData/Roaming/npm/rc.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/rc.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/rc"
      - "C:/ProgramData/mingw64/mingw64/bin/rc.com"
      - "C:/ProgramData/mingw64/mingw64/bin/rc.exe"
      - "C:/ProgramData/mingw64/mingw64/bin/rc"
      - "C:/Program Files/bin/rc.com"
      - "C:/Program Files/bin/rc.exe"
      - "C:/Program Files/bin/rc"
      - "C:/Program Files/sbin/rc.com"
      - "C:/Program Files/sbin/rc.exe"
      - "C:/Program Files/sbin/rc"
      - "C:/Program Files/rc.com"
      - "C:/Program Files/rc.exe"
      - "C:/Program Files/rc"
      - "C:/Program Files (x86)/bin/rc.com"
      - "C:/Program Files (x86)/bin/rc.exe"
      - "C:/Program Files (x86)/bin/rc"
      - "C:/Program Files (x86)/sbin/rc.com"
      - "C:/Program Files (x86)/sbin/rc.exe"
      - "C:/Program Files (x86)/sbin/rc"
      - "C:/Program Files (x86)/rc.com"
      - "C:/Program Files (x86)/rc.exe"
      - "C:/Program Files (x86)/rc"
      - "C:/Program Files/CMake/bin/rc.com"
      - "C:/Program Files/CMake/bin/rc.exe"
      - "C:/Program Files/CMake/bin/rc"
      - "C:/Program Files/CMake/sbin/rc.com"
      - "C:/Program Files/CMake/sbin/rc.exe"
      - "C:/Program Files/CMake/sbin/rc"
      - "C:/Program Files/CMake/rc.com"
      - "C:/Program Files/CMake/rc.exe"
      - "C:/Program Files/CMake/rc"
      - "C:/Program Files (x86)/MySDL3Game/bin/rc.com"
      - "C:/Program Files (x86)/MySDL3Game/bin/rc.exe"
      - "C:/Program Files (x86)/MySDL3Game/bin/rc"
      - "C:/Program Files (x86)/MySDL3Game/sbin/rc.com"
      - "C:/Program Files (x86)/MySDL3Game/sbin/rc.exe"
      - "C:/Program Files (x86)/MySDL3Game/sbin/rc"
      - "C:/Program Files (x86)/MySDL3Game/rc.com"
      - "C:/Program Files (x86)/MySDL3Game/rc.exe"
      - "C:/Program Files (x86)/MySDL3Game/rc"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Users\\<USER>\\scoop\\shims"
        - "C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ProgramData\\mingw64\\mingw64\\bin"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/MySDL3Game"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/MySDL3Game"
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-hz2s5o"
      binary: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-hz2s5o"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-hz2s5o'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_0dab0.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 8/11/2025 12:42:41 AM.
        
        Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hz2s5o\\cmTC_0dab0.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_0dab0.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hz2s5o\\Debug\\".
          Creating directory "cmTC_0dab0.dir\\Debug\\cmTC_0dab0.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_0dab0.dir\\Debug\\cmTC_0dab0.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_0dab0.dir\\Debug\\cmTC_0dab0.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Fo"cmTC_0dab0.dir\\Debug\\\\" /Fd"cmTC_0dab0.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCCompilerABI.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34809 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Fo"cmTC_0dab0.dir\\Debug\\\\" /Fd"cmTC_0dab0.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /OUT:"D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hz2s5o\\Debug\\cmTC_0dab0.exe" /INCREMENTAL /ILK:"cmTC_0dab0.dir\\Debug\\cmTC_0dab0.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-hz2s5o/Debug/cmTC_0dab0.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-hz2s5o/Debug/cmTC_0dab0.lib" /MACHINE:X64  /machine:x64 cmTC_0dab0.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_0dab0.vcxproj -> D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hz2s5o\\Debug\\cmTC_0dab0.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_0dab0.dir\\Debug\\cmTC_0dab0.tlog\\unsuccessfulbuild".
          Touching "cmTC_0dab0.dir\\Debug\\cmTC_0dab0.tlog\\cmTC_0dab0.lastbuildstate".
        Done Building Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hz2s5o\\cmTC_0dab0.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.59
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.43.34809.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-7xpyro"
      binary: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-7xpyro"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-7xpyro'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_3e05b.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 8/11/2025 12:42:42 AM.
        
        Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7xpyro\\cmTC_3e05b.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_3e05b.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7xpyro\\Debug\\".
          Creating directory "cmTC_3e05b.dir\\Debug\\cmTC_3e05b.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_3e05b.dir\\Debug\\cmTC_3e05b.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_3e05b.dir\\Debug\\cmTC_3e05b.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /GR /Fo"cmTC_3e05b.dir\\Debug\\\\" /Fd"cmTC_3e05b.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34809 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /GR /Fo"cmTC_3e05b.dir\\Debug\\\\" /Fd"cmTC_3e05b.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /OUT:"D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7xpyro\\Debug\\cmTC_3e05b.exe" /INCREMENTAL /ILK:"cmTC_3e05b.dir\\Debug\\cmTC_3e05b.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-7xpyro/Debug/cmTC_3e05b.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-7xpyro/Debug/cmTC_3e05b.lib" /MACHINE:X64  /machine:x64 cmTC_3e05b.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_3e05b.vcxproj -> D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7xpyro\\Debug\\cmTC_3e05b.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_3e05b.dir\\Debug\\cmTC_3e05b.tlog\\unsuccessfulbuild".
          Touching "cmTC_3e05b.dir\\Debug\\cmTC_3e05b.tlog\\cmTC_3e05b.lastbuildstate".
        Done Building Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7xpyro\\cmTC_3e05b.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.64
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.43.34809.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
...

---
events:
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/FindPkgConfig.cmake:69 (find_program)"
      - "libs/SDL3/CMakeLists.txt:65 (find_package)"
    mode: "program"
    variable: "PKG_CONFIG_EXECUTABLE"
    description: "pkg-config executable"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "pkg-config.bat"
      - "pkg-config"
      - "pkgconf"
    candidate_directories:
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Users/<USER>/scoop/shims/"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ProgramData/mingw64/mingw64/bin/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/MySDL3Game/bin/"
      - "C:/Program Files (x86)/MySDL3Game/sbin/"
      - "C:/Program Files (x86)/MySDL3Game/"
    searched_directories:
      - "C:/Program Files/Oculus/Support/oculus-runtime/pkg-config.bat.com"
      - "C:/Program Files/Oculus/Support/oculus-runtime/pkg-config.bat.exe"
      - "C:/Program Files/Oculus/Support/oculus-runtime/pkg-config.bat"
      - "C:/Program Files/Oculus/Support/oculus-runtime/pkg-config.com"
      - "C:/Program Files/Oculus/Support/oculus-runtime/pkg-config.exe"
      - "C:/Program Files/Oculus/Support/oculus-runtime/pkg-config"
      - "C:/Program Files/Oculus/Support/oculus-runtime/pkgconf.com"
      - "C:/Program Files/Oculus/Support/oculus-runtime/pkgconf.exe"
      - "C:/Program Files/Oculus/Support/oculus-runtime/pkgconf"
      - "C:/Windows/System32/pkg-config.bat.com"
      - "C:/Windows/System32/pkg-config.bat.exe"
      - "C:/Windows/System32/pkg-config.bat"
      - "C:/Windows/System32/pkg-config.com"
      - "C:/Windows/System32/pkg-config.exe"
      - "C:/Windows/System32/pkg-config"
      - "C:/Windows/System32/pkgconf.com"
      - "C:/Windows/System32/pkgconf.exe"
      - "C:/Windows/System32/pkgconf"
      - "C:/Windows/pkg-config.bat.com"
      - "C:/Windows/pkg-config.bat.exe"
      - "C:/Windows/pkg-config.bat"
      - "C:/Windows/pkg-config.com"
      - "C:/Windows/pkg-config.exe"
      - "C:/Windows/pkg-config"
      - "C:/Windows/pkgconf.com"
      - "C:/Windows/pkgconf.exe"
      - "C:/Windows/pkgconf"
      - "C:/Windows/System32/wbem/pkg-config.bat.com"
      - "C:/Windows/System32/wbem/pkg-config.bat.exe"
      - "C:/Windows/System32/wbem/pkg-config.bat"
      - "C:/Windows/System32/wbem/pkg-config.com"
      - "C:/Windows/System32/wbem/pkg-config.exe"
      - "C:/Windows/System32/wbem/pkg-config"
      - "C:/Windows/System32/wbem/pkgconf.com"
      - "C:/Windows/System32/wbem/pkgconf.exe"
      - "C:/Windows/System32/wbem/pkgconf"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/pkg-config.bat.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/pkg-config.bat.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/pkg-config.bat"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/pkg-config.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/pkg-config.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/pkg-config"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/pkgconf.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/pkgconf.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/pkgconf"
      - "C:/Windows/System32/OpenSSH/pkg-config.bat.com"
      - "C:/Windows/System32/OpenSSH/pkg-config.bat.exe"
      - "C:/Windows/System32/OpenSSH/pkg-config.bat"
      - "C:/Windows/System32/OpenSSH/pkg-config.com"
      - "C:/Windows/System32/OpenSSH/pkg-config.exe"
      - "C:/Windows/System32/OpenSSH/pkg-config"
      - "C:/Windows/System32/OpenSSH/pkgconf.com"
      - "C:/Windows/System32/OpenSSH/pkgconf.exe"
      - "C:/Windows/System32/OpenSSH/pkgconf"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/pkg-config.bat.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/pkg-config.bat.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/pkg-config.bat"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/pkg-config.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/pkg-config.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/pkg-config"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/pkgconf.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/pkgconf.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/pkgconf"
      - "C:/Program Files/dotnet/pkg-config.bat.com"
      - "C:/Program Files/dotnet/pkg-config.bat.exe"
      - "C:/Program Files/dotnet/pkg-config.bat"
      - "C:/Program Files/dotnet/pkg-config.com"
      - "C:/Program Files/dotnet/pkg-config.exe"
      - "C:/Program Files/dotnet/pkg-config"
      - "C:/Program Files/dotnet/pkgconf.com"
      - "C:/Program Files/dotnet/pkgconf.exe"
      - "C:/Program Files/dotnet/pkgconf"
      - "C:/Program Files/Git/cmd/pkg-config.bat.com"
      - "C:/Program Files/Git/cmd/pkg-config.bat.exe"
      - "C:/Program Files/Git/cmd/pkg-config.bat"
      - "C:/Program Files/Git/cmd/pkg-config.com"
      - "C:/Program Files/Git/cmd/pkg-config.exe"
      - "C:/Program Files/Git/cmd/pkg-config"
      - "C:/Program Files/Git/cmd/pkgconf.com"
      - "C:/Program Files/Git/cmd/pkgconf.exe"
      - "C:/Program Files/Git/cmd/pkgconf"
      - "C:/Program Files/nodejs/pkg-config.bat.com"
      - "C:/Program Files/nodejs/pkg-config.bat.exe"
      - "C:/Program Files/nodejs/pkg-config.bat"
      - "C:/Program Files/nodejs/pkg-config.com"
      - "C:/Program Files/nodejs/pkg-config.exe"
      - "C:/Program Files/nodejs/pkg-config"
      - "C:/Program Files/nodejs/pkgconf.com"
      - "C:/Program Files/nodejs/pkgconf.exe"
      - "C:/Program Files/nodejs/pkgconf"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/pkg-config.bat.com"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/pkg-config.bat.exe"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/pkg-config.bat"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/pkg-config.com"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/pkg-config.exe"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/pkg-config"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/pkgconf.com"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/pkgconf.exe"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/pkgconf"
      - "C:/Program Files/Docker/Docker/resources/bin/pkg-config.bat.com"
      - "C:/Program Files/Docker/Docker/resources/bin/pkg-config.bat.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/pkg-config.bat"
      - "C:/Program Files/Docker/Docker/resources/bin/pkg-config.com"
      - "C:/Program Files/Docker/Docker/resources/bin/pkg-config.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/pkg-config"
      - "C:/Program Files/Docker/Docker/resources/bin/pkgconf.com"
      - "C:/Program Files/Docker/Docker/resources/bin/pkgconf.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/pkgconf"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/pkg-config.bat.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/pkg-config.bat.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/pkg-config.bat"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/pkg-config.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/pkg-config.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/pkg-config"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/pkgconf.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/pkgconf.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/pkgconf"
      - "C:/Program Files/CMake/bin/pkg-config.bat.com"
      - "C:/Program Files/CMake/bin/pkg-config.bat.exe"
      - "C:/Program Files/CMake/bin/pkg-config.bat"
      - "C:/Program Files/CMake/bin/pkg-config.com"
      - "C:/Program Files/CMake/bin/pkg-config.exe"
      - "C:/Program Files/CMake/bin/pkg-config"
      - "C:/Program Files/CMake/bin/pkgconf.com"
      - "C:/Program Files/CMake/bin/pkgconf.exe"
      - "C:/Program Files/CMake/bin/pkgconf"
      - "C:/ProgramData/chocolatey/bin/pkg-config.bat.com"
      - "C:/ProgramData/chocolatey/bin/pkg-config.bat.exe"
      - "C:/ProgramData/chocolatey/bin/pkg-config.bat"
      - "C:/ProgramData/chocolatey/bin/pkg-config.com"
      - "C:/ProgramData/chocolatey/bin/pkg-config.exe"
      - "C:/ProgramData/chocolatey/bin/pkg-config"
      - "C:/ProgramData/chocolatey/bin/pkgconf.com"
      - "C:/ProgramData/chocolatey/bin/pkgconf.exe"
      - "C:/ProgramData/chocolatey/bin/pkgconf"
      - "C:/Users/<USER>/scoop/shims/pkg-config.bat.com"
      - "C:/Users/<USER>/scoop/shims/pkg-config.bat.exe"
      - "C:/Users/<USER>/scoop/shims/pkg-config.bat"
      - "C:/Users/<USER>/scoop/shims/pkg-config.com"
      - "C:/Users/<USER>/scoop/shims/pkg-config.exe"
      - "C:/Users/<USER>/scoop/shims/pkg-config"
      - "C:/Users/<USER>/scoop/shims/pkgconf.com"
      - "C:/Users/<USER>/scoop/shims/pkgconf.exe"
      - "C:/Users/<USER>/scoop/shims/pkgconf"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/pkg-config.bat.com"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/pkg-config.bat.exe"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/pkg-config.bat"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/pkg-config.com"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/pkg-config.exe"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/pkg-config"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/pkgconf.com"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/pkgconf.exe"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/pkgconf"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/pkg-config.bat.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/pkg-config.bat.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/pkg-config.bat"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/pkg-config.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/pkg-config.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/pkg-config"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/pkgconf.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/pkgconf.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/pkgconf"
      - "C:/Users/<USER>/.dotnet/tools/pkg-config.bat.com"
      - "C:/Users/<USER>/.dotnet/tools/pkg-config.bat.exe"
      - "C:/Users/<USER>/.dotnet/tools/pkg-config.bat"
      - "C:/Users/<USER>/.dotnet/tools/pkg-config.com"
      - "C:/Users/<USER>/.dotnet/tools/pkg-config.exe"
      - "C:/Users/<USER>/.dotnet/tools/pkg-config"
      - "C:/Users/<USER>/.dotnet/tools/pkgconf.com"
      - "C:/Users/<USER>/.dotnet/tools/pkgconf.exe"
      - "C:/Users/<USER>/.dotnet/tools/pkgconf"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/pkg-config.bat.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/pkg-config.bat.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/pkg-config.bat"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/pkg-config.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/pkg-config.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/pkg-config"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/pkgconf.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/pkgconf.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/pkgconf"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/pkg-config.bat.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/pkg-config.bat.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/pkg-config.bat"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/pkg-config.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/pkg-config.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/pkg-config"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/pkgconf.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/pkgconf.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/pkgconf"
      - "C:/Users/<USER>/AppData/Roaming/npm/pkg-config.bat.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/pkg-config.bat.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/pkg-config.bat"
      - "C:/Users/<USER>/AppData/Roaming/npm/pkg-config.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/pkg-config.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/pkg-config"
      - "C:/Users/<USER>/AppData/Roaming/npm/pkgconf.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/pkgconf.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/pkgconf"
      - "C:/ProgramData/mingw64/mingw64/bin/pkg-config.bat.com"
      - "C:/ProgramData/mingw64/mingw64/bin/pkg-config.bat.exe"
      - "C:/ProgramData/mingw64/mingw64/bin/pkg-config.bat"
      - "C:/ProgramData/mingw64/mingw64/bin/pkg-config.com"
      - "C:/ProgramData/mingw64/mingw64/bin/pkg-config.exe"
      - "C:/ProgramData/mingw64/mingw64/bin/pkg-config"
      - "C:/ProgramData/mingw64/mingw64/bin/pkgconf.com"
      - "C:/ProgramData/mingw64/mingw64/bin/pkgconf.exe"
      - "C:/ProgramData/mingw64/mingw64/bin/pkgconf"
      - "C:/Program Files/bin/pkg-config.bat.com"
      - "C:/Program Files/bin/pkg-config.bat.exe"
      - "C:/Program Files/bin/pkg-config.bat"
      - "C:/Program Files/bin/pkg-config.com"
      - "C:/Program Files/bin/pkg-config.exe"
      - "C:/Program Files/bin/pkg-config"
      - "C:/Program Files/bin/pkgconf.com"
      - "C:/Program Files/bin/pkgconf.exe"
      - "C:/Program Files/bin/pkgconf"
      - "C:/Program Files/sbin/pkg-config.bat.com"
      - "C:/Program Files/sbin/pkg-config.bat.exe"
      - "C:/Program Files/sbin/pkg-config.bat"
      - "C:/Program Files/sbin/pkg-config.com"
      - "C:/Program Files/sbin/pkg-config.exe"
      - "C:/Program Files/sbin/pkg-config"
      - "C:/Program Files/sbin/pkgconf.com"
      - "C:/Program Files/sbin/pkgconf.exe"
      - "C:/Program Files/sbin/pkgconf"
      - "C:/Program Files/pkg-config.bat.com"
      - "C:/Program Files/pkg-config.bat.exe"
      - "C:/Program Files/pkg-config.bat"
      - "C:/Program Files/pkg-config.com"
      - "C:/Program Files/pkg-config.exe"
      - "C:/Program Files/pkg-config"
      - "C:/Program Files/pkgconf.com"
      - "C:/Program Files/pkgconf.exe"
      - "C:/Program Files/pkgconf"
      - "C:/Program Files (x86)/bin/pkg-config.bat.com"
      - "C:/Program Files (x86)/bin/pkg-config.bat.exe"
      - "C:/Program Files (x86)/bin/pkg-config.bat"
      - "C:/Program Files (x86)/bin/pkg-config.com"
      - "C:/Program Files (x86)/bin/pkg-config.exe"
      - "C:/Program Files (x86)/bin/pkg-config"
      - "C:/Program Files (x86)/bin/pkgconf.com"
      - "C:/Program Files (x86)/bin/pkgconf.exe"
      - "C:/Program Files (x86)/bin/pkgconf"
      - "C:/Program Files (x86)/sbin/pkg-config.bat.com"
      - "C:/Program Files (x86)/sbin/pkg-config.bat.exe"
      - "C:/Program Files (x86)/sbin/pkg-config.bat"
      - "C:/Program Files (x86)/sbin/pkg-config.com"
      - "C:/Program Files (x86)/sbin/pkg-config.exe"
      - "C:/Program Files (x86)/sbin/pkg-config"
      - "C:/Program Files (x86)/sbin/pkgconf.com"
      - "C:/Program Files (x86)/sbin/pkgconf.exe"
      - "C:/Program Files (x86)/sbin/pkgconf"
      - "C:/Program Files (x86)/pkg-config.bat.com"
      - "C:/Program Files (x86)/pkg-config.bat.exe"
      - "C:/Program Files (x86)/pkg-config.bat"
      - "C:/Program Files (x86)/pkg-config.com"
      - "C:/Program Files (x86)/pkg-config.exe"
      - "C:/Program Files (x86)/pkg-config"
      - "C:/Program Files (x86)/pkgconf.com"
      - "C:/Program Files (x86)/pkgconf.exe"
      - "C:/Program Files (x86)/pkgconf"
      - "C:/Program Files/CMake/bin/pkg-config.bat.com"
      - "C:/Program Files/CMake/bin/pkg-config.bat.exe"
      - "C:/Program Files/CMake/bin/pkg-config.bat"
      - "C:/Program Files/CMake/bin/pkg-config.com"
      - "C:/Program Files/CMake/bin/pkg-config.exe"
      - "C:/Program Files/CMake/bin/pkg-config"
      - "C:/Program Files/CMake/bin/pkgconf.com"
      - "C:/Program Files/CMake/bin/pkgconf.exe"
      - "C:/Program Files/CMake/bin/pkgconf"
      - "C:/Program Files/CMake/sbin/pkg-config.bat.com"
      - "C:/Program Files/CMake/sbin/pkg-config.bat.exe"
      - "C:/Program Files/CMake/sbin/pkg-config.bat"
      - "C:/Program Files/CMake/sbin/pkg-config.com"
      - "C:/Program Files/CMake/sbin/pkg-config.exe"
      - "C:/Program Files/CMake/sbin/pkg-config"
      - "C:/Program Files/CMake/sbin/pkgconf.com"
      - "C:/Program Files/CMake/sbin/pkgconf.exe"
      - "C:/Program Files/CMake/sbin/pkgconf"
      - "C:/Program Files/CMake/pkg-config.bat.com"
      - "C:/Program Files/CMake/pkg-config.bat.exe"
      - "C:/Program Files/CMake/pkg-config.bat"
      - "C:/Program Files/CMake/pkg-config.com"
      - "C:/Program Files/CMake/pkg-config.exe"
      - "C:/Program Files/CMake/pkg-config"
      - "C:/Program Files/CMake/pkgconf.com"
      - "C:/Program Files/CMake/pkgconf.exe"
      - "C:/Program Files/CMake/pkgconf"
      - "C:/Program Files (x86)/MySDL3Game/bin/pkg-config.bat.com"
      - "C:/Program Files (x86)/MySDL3Game/bin/pkg-config.bat.exe"
      - "C:/Program Files (x86)/MySDL3Game/bin/pkg-config.bat"
      - "C:/Program Files (x86)/MySDL3Game/bin/pkg-config.com"
      - "C:/Program Files (x86)/MySDL3Game/bin/pkg-config.exe"
      - "C:/Program Files (x86)/MySDL3Game/bin/pkg-config"
      - "C:/Program Files (x86)/MySDL3Game/bin/pkgconf.com"
      - "C:/Program Files (x86)/MySDL3Game/bin/pkgconf.exe"
      - "C:/Program Files (x86)/MySDL3Game/bin/pkgconf"
      - "C:/Program Files (x86)/MySDL3Game/sbin/pkg-config.bat.com"
      - "C:/Program Files (x86)/MySDL3Game/sbin/pkg-config.bat.exe"
      - "C:/Program Files (x86)/MySDL3Game/sbin/pkg-config.bat"
      - "C:/Program Files (x86)/MySDL3Game/sbin/pkg-config.com"
      - "C:/Program Files (x86)/MySDL3Game/sbin/pkg-config.exe"
      - "C:/Program Files (x86)/MySDL3Game/sbin/pkg-config"
      - "C:/Program Files (x86)/MySDL3Game/sbin/pkgconf.com"
      - "C:/Program Files (x86)/MySDL3Game/sbin/pkgconf.exe"
      - "C:/Program Files (x86)/MySDL3Game/sbin/pkgconf"
      - "C:/Program Files (x86)/MySDL3Game/pkg-config.bat.com"
      - "C:/Program Files (x86)/MySDL3Game/pkg-config.bat.exe"
      - "C:/Program Files (x86)/MySDL3Game/pkg-config.bat"
      - "C:/Program Files (x86)/MySDL3Game/pkg-config.com"
      - "C:/Program Files (x86)/MySDL3Game/pkg-config.exe"
      - "C:/Program Files (x86)/MySDL3Game/pkg-config"
      - "C:/Program Files (x86)/MySDL3Game/pkgconf.com"
      - "C:/Program Files (x86)/MySDL3Game/pkgconf.exe"
      - "C:/Program Files (x86)/MySDL3Game/pkgconf"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Users\\<USER>\\scoop\\shims"
        - "C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ProgramData\\mingw64\\mingw64\\bin"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/MySDL3Game"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/MySDL3Game"
  -
    kind: "try_compile-v1"
    backtrace:
      - "libs/SDL3/cmake/sdlcpu.cmake:93 (try_compile)"
      - "libs/SDL3/CMakeLists.txt:82 (SDL_DetectTargetCPUArchitectures)"
    directories:
      source: "D:/Frank Work All/CPP/f0/build/libs/SDL3/CMakeFiles/CMakeTmp/SDL_detect_arch/CMakeFiles/CMakeTmp"
      binary: "D:/Frank Work All/CPP/f0/build/libs/SDL3/CMakeFiles/CMakeTmp/SDL_detect_arch/CMakeFiles/CMakeTmp"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/Frank Work All/CPP/f0/libs/SDL3/cmake"
    buildResult:
      variable: "SDL_CPU_CHECK_ALL"
      cached: true
      stdout: |
        Change Dir: 'D:/Frank Work All/CPP/f0/build/libs/SDL3/CMakeFiles/CMakeTmp/SDL_detect_arch/CMakeFiles/CMakeTmp'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_068c2.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 8/11/2025 12:46:22 AM.
        
        Project "D:\\Frank Work All\\CPP\\f0\\build\\libs\\SDL3\\CMakeFiles\\CMakeTmp\\SDL_detect_arch\\CMakeFiles\\CMakeTmp\\cmTC_068c2.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_068c2.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\Frank Work All\\CPP\\f0\\build\\libs\\SDL3\\CMakeFiles\\CMakeTmp\\SDL_detect_arch\\CMakeFiles\\CMakeTmp\\Debug\\".
          Creating directory "cmTC_068c2.dir\\Debug\\cmTC_068c2.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_068c2.dir\\Debug\\cmTC_068c2.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_068c2.dir\\Debug\\cmTC_068c2.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Fo"cmTC_068c2.dir\\Debug\\\\" /Fd"D:\\Frank Work All\\CPP\\f0\\build\\libs\\SDL3\\CMakeFiles\\CMakeTmp\\SDL_detect_arch\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_068c2.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\Frank Work All\\CPP\\f0\\build\\libs\\SDL3\\CMakeFiles\\CMakeTmp\\SDL_detect_arch.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34809 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Fo"cmTC_068c2.dir\\Debug\\\\" /Fd"D:\\Frank Work All\\CPP\\f0\\build\\libs\\SDL3\\CMakeFiles\\CMakeTmp\\SDL_detect_arch\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_068c2.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\Frank Work All\\CPP\\f0\\build\\libs\\SDL3\\CMakeFiles\\CMakeTmp\\SDL_detect_arch.c"
          SDL_detect_arch.c
        Lib:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\Lib.exe /OUT:"D:\\Frank Work All\\CPP\\f0\\build\\libs\\SDL3\\CMakeFiles\\CMakeTmp\\SDL_detect_arch\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_068c2.lib" /NOLOGO /MACHINE:X64  /machine:x64 cmTC_068c2.dir\\Debug\\SDL_detect_arch.obj
          cmTC_068c2.vcxproj -> D:\\Frank Work All\\CPP\\f0\\build\\libs\\SDL3\\CMakeFiles\\CMakeTmp\\SDL_detect_arch\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_068c2.lib
        FinalizeBuildStatus:
          Deleting file "cmTC_068c2.dir\\Debug\\cmTC_068c2.tlog\\unsuccessfulbuild".
          Touching "cmTC_068c2.dir\\Debug\\cmTC_068c2.tlog\\cmTC_068c2.lastbuildstate".
        Done Building Project "D:\\Frank Work All\\CPP\\f0\\build\\libs\\SDL3\\CMakeFiles\\CMakeTmp\\SDL_detect_arch\\CMakeFiles\\CMakeTmp\\cmTC_068c2.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.47
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CheckCSourceCompiles.cmake:103 (cmake_check_source_compiles)"
      - "libs/SDL3/CMakeLists.txt:683 (check_c_source_compiles)"
    checks:
      - "Performing Test COMPILER_SUPPORTS_MMX"
    directories:
      source: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-0yaq61"
      binary: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-0yaq61"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od "
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/Frank Work All/CPP/f0/libs/SDL3/cmake"
      CMAKE_MSVC_RUNTIME_CHECKS: ""
    buildResult:
      variable: "COMPILER_SUPPORTS_MMX"
      cached: true
      stdout: |
        Change Dir: 'D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-0yaq61'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_e4f3f.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 8/11/2025 12:46:22 AM.
        
        Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0yaq61\\cmTC_e4f3f.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_e4f3f.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0yaq61\\Debug\\".
          Creating directory "cmTC_e4f3f.dir\\Debug\\cmTC_e4f3f.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_e4f3f.dir\\Debug\\cmTC_e4f3f.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_e4f3f.dir\\Debug\\cmTC_e4f3f.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _GNU_SOURCE=1 /D COMPILER_SUPPORTS_MMX /D "CMAKE_INTDIR=\\"Debug\\"" /MDd /GS /fp:precise /Fo"cmTC_e4f3f.dir\\Debug\\\\" /Fd"cmTC_e4f3f.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0yaq61\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34809 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _GNU_SOURCE=1 /D COMPILER_SUPPORTS_MMX /D "CMAKE_INTDIR=\\"Debug\\"" /MDd /GS /fp:precise /Fo"cmTC_e4f3f.dir\\Debug\\\\" /Fd"cmTC_e4f3f.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0yaq61\\src.c"
          src.c
        D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0yaq61\\src.c(5,25): error C2440: '=': cannot convert from 'int' to '__m64' [D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0yaq61\\cmTC_e4f3f.vcxproj]
        Done Building Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0yaq61\\cmTC_e4f3f.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0yaq61\\cmTC_e4f3f.vcxproj" (default target) (1) ->
        (ClCompile target) -> 
          D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0yaq61\\src.c(5,25): error C2440: '=': cannot convert from 'int' to '__m64' [D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0yaq61\\cmTC_e4f3f.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.38
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CheckCSourceCompiles.cmake:103 (cmake_check_source_compiles)"
      - "libs/SDL3/CMakeLists.txt:704 (check_c_source_compiles)"
    checks:
      - "Performing Test COMPILER_SUPPORTS_SSE"
    directories:
      source: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-6twf5e"
      binary: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-6twf5e"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od "
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/Frank Work All/CPP/f0/libs/SDL3/cmake"
      CMAKE_MSVC_RUNTIME_CHECKS: ""
    buildResult:
      variable: "COMPILER_SUPPORTS_SSE"
      cached: true
      stdout: |
        Change Dir: 'D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-6twf5e'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_aa0fb.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 8/11/2025 12:46:23 AM.
        
        Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6twf5e\\cmTC_aa0fb.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_aa0fb.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6twf5e\\Debug\\".
          Creating directory "cmTC_aa0fb.dir\\Debug\\cmTC_aa0fb.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_aa0fb.dir\\Debug\\cmTC_aa0fb.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_aa0fb.dir\\Debug\\cmTC_aa0fb.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _GNU_SOURCE=1 /D COMPILER_SUPPORTS_SSE /D "CMAKE_INTDIR=\\"Debug\\"" /MDd /GS /fp:precise /Fo"cmTC_aa0fb.dir\\Debug\\\\" /Fd"cmTC_aa0fb.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6twf5e\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34809 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _GNU_SOURCE=1 /D COMPILER_SUPPORTS_SSE /D "CMAKE_INTDIR=\\"Debug\\"" /MDd /GS /fp:precise /Fo"cmTC_aa0fb.dir\\Debug\\\\" /Fd"cmTC_aa0fb.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6twf5e\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /OUT:"D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6twf5e\\Debug\\cmTC_aa0fb.exe" /INCREMENTAL /ILK:"cmTC_aa0fb.dir\\Debug\\cmTC_aa0fb.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-6twf5e/Debug/cmTC_aa0fb.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-6twf5e/Debug/cmTC_aa0fb.lib" /MACHINE:X64  /machine:x64 cmTC_aa0fb.dir\\Debug\\src.obj
          cmTC_aa0fb.vcxproj -> D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6twf5e\\Debug\\cmTC_aa0fb.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_aa0fb.dir\\Debug\\cmTC_aa0fb.tlog\\unsuccessfulbuild".
          Touching "cmTC_aa0fb.dir\\Debug\\cmTC_aa0fb.tlog\\cmTC_aa0fb.lastbuildstate".
        Done Building Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6twf5e\\cmTC_aa0fb.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.56
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CheckCSourceCompiles.cmake:103 (cmake_check_source_compiles)"
      - "libs/SDL3/CMakeLists.txt:725 (check_c_source_compiles)"
    checks:
      - "Performing Test COMPILER_SUPPORTS_SSE2"
    directories:
      source: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-ytom91"
      binary: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-ytom91"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od "
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/Frank Work All/CPP/f0/libs/SDL3/cmake"
      CMAKE_MSVC_RUNTIME_CHECKS: ""
    buildResult:
      variable: "COMPILER_SUPPORTS_SSE2"
      cached: true
      stdout: |
        Change Dir: 'D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-ytom91'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_accdf.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 8/11/2025 12:46:24 AM.
        
        Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ytom91\\cmTC_accdf.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_accdf.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ytom91\\Debug\\".
          Creating directory "cmTC_accdf.dir\\Debug\\cmTC_accdf.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_accdf.dir\\Debug\\cmTC_accdf.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_accdf.dir\\Debug\\cmTC_accdf.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _GNU_SOURCE=1 /D COMPILER_SUPPORTS_SSE2 /D "CMAKE_INTDIR=\\"Debug\\"" /MDd /GS /fp:precise /Fo"cmTC_accdf.dir\\Debug\\\\" /Fd"cmTC_accdf.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ytom91\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34809 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _GNU_SOURCE=1 /D COMPILER_SUPPORTS_SSE2 /D "CMAKE_INTDIR=\\"Debug\\"" /MDd /GS /fp:precise /Fo"cmTC_accdf.dir\\Debug\\\\" /Fd"cmTC_accdf.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ytom91\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /OUT:"D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ytom91\\Debug\\cmTC_accdf.exe" /INCREMENTAL /ILK:"cmTC_accdf.dir\\Debug\\cmTC_accdf.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-ytom91/Debug/cmTC_accdf.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-ytom91/Debug/cmTC_accdf.lib" /MACHINE:X64  /machine:x64 cmTC_accdf.dir\\Debug\\src.obj
          cmTC_accdf.vcxproj -> D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ytom91\\Debug\\cmTC_accdf.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_accdf.dir\\Debug\\cmTC_accdf.tlog\\unsuccessfulbuild".
          Touching "cmTC_accdf.dir\\Debug\\cmTC_accdf.tlog\\cmTC_accdf.lastbuildstate".
        Done Building Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ytom91\\cmTC_accdf.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.58
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CheckCSourceCompiles.cmake:103 (cmake_check_source_compiles)"
      - "libs/SDL3/CMakeLists.txt:746 (check_c_source_compiles)"
    checks:
      - "Performing Test COMPILER_SUPPORTS_SSE3"
    directories:
      source: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-ox1do4"
      binary: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-ox1do4"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od "
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/Frank Work All/CPP/f0/libs/SDL3/cmake"
      CMAKE_MSVC_RUNTIME_CHECKS: ""
    buildResult:
      variable: "COMPILER_SUPPORTS_SSE3"
      cached: true
      stdout: |
        Change Dir: 'D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-ox1do4'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_430d5.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 8/11/2025 12:46:25 AM.
        
        Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ox1do4\\cmTC_430d5.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_430d5.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ox1do4\\Debug\\".
          Creating directory "cmTC_430d5.dir\\Debug\\cmTC_430d5.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_430d5.dir\\Debug\\cmTC_430d5.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_430d5.dir\\Debug\\cmTC_430d5.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _GNU_SOURCE=1 /D COMPILER_SUPPORTS_SSE3 /D "CMAKE_INTDIR=\\"Debug\\"" /MDd /GS /fp:precise /Fo"cmTC_430d5.dir\\Debug\\\\" /Fd"cmTC_430d5.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ox1do4\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34809 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _GNU_SOURCE=1 /D COMPILER_SUPPORTS_SSE3 /D "CMAKE_INTDIR=\\"Debug\\"" /MDd /GS /fp:precise /Fo"cmTC_430d5.dir\\Debug\\\\" /Fd"cmTC_430d5.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ox1do4\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /OUT:"D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ox1do4\\Debug\\cmTC_430d5.exe" /INCREMENTAL /ILK:"cmTC_430d5.dir\\Debug\\cmTC_430d5.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-ox1do4/Debug/cmTC_430d5.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-ox1do4/Debug/cmTC_430d5.lib" /MACHINE:X64  /machine:x64 cmTC_430d5.dir\\Debug\\src.obj
          cmTC_430d5.vcxproj -> D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ox1do4\\Debug\\cmTC_430d5.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_430d5.dir\\Debug\\cmTC_430d5.tlog\\unsuccessfulbuild".
          Touching "cmTC_430d5.dir\\Debug\\cmTC_430d5.tlog\\cmTC_430d5.lastbuildstate".
        Done Building Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ox1do4\\cmTC_430d5.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.70
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CheckCSourceCompiles.cmake:103 (cmake_check_source_compiles)"
      - "libs/SDL3/CMakeLists.txt:767 (check_c_source_compiles)"
    checks:
      - "Performing Test COMPILER_SUPPORTS_SSE4_1"
    directories:
      source: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-1q0mr2"
      binary: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-1q0mr2"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od "
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/Frank Work All/CPP/f0/libs/SDL3/cmake"
      CMAKE_MSVC_RUNTIME_CHECKS: ""
    buildResult:
      variable: "COMPILER_SUPPORTS_SSE4_1"
      cached: true
      stdout: |
        Change Dir: 'D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-1q0mr2'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_e101a.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 8/11/2025 12:46:25 AM.
        
        Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1q0mr2\\cmTC_e101a.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_e101a.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1q0mr2\\Debug\\".
          Creating directory "cmTC_e101a.dir\\Debug\\cmTC_e101a.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_e101a.dir\\Debug\\cmTC_e101a.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_e101a.dir\\Debug\\cmTC_e101a.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _GNU_SOURCE=1 /D COMPILER_SUPPORTS_SSE4_1 /D "CMAKE_INTDIR=\\"Debug\\"" /MDd /GS /fp:precise /Fo"cmTC_e101a.dir\\Debug\\\\" /Fd"cmTC_e101a.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1q0mr2\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34809 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _GNU_SOURCE=1 /D COMPILER_SUPPORTS_SSE4_1 /D "CMAKE_INTDIR=\\"Debug\\"" /MDd /GS /fp:precise /Fo"cmTC_e101a.dir\\Debug\\\\" /Fd"cmTC_e101a.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1q0mr2\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /OUT:"D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1q0mr2\\Debug\\cmTC_e101a.exe" /INCREMENTAL /ILK:"cmTC_e101a.dir\\Debug\\cmTC_e101a.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-1q0mr2/Debug/cmTC_e101a.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-1q0mr2/Debug/cmTC_e101a.lib" /MACHINE:X64  /machine:x64 cmTC_e101a.dir\\Debug\\src.obj
          cmTC_e101a.vcxproj -> D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1q0mr2\\Debug\\cmTC_e101a.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_e101a.dir\\Debug\\cmTC_e101a.tlog\\unsuccessfulbuild".
          Touching "cmTC_e101a.dir\\Debug\\cmTC_e101a.tlog\\cmTC_e101a.lastbuildstate".
        Done Building Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1q0mr2\\cmTC_e101a.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.53
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CheckCSourceCompiles.cmake:103 (cmake_check_source_compiles)"
      - "libs/SDL3/CMakeLists.txt:788 (check_c_source_compiles)"
    checks:
      - "Performing Test COMPILER_SUPPORTS_SSE4_2"
    directories:
      source: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-qc0mt2"
      binary: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-qc0mt2"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od "
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/Frank Work All/CPP/f0/libs/SDL3/cmake"
      CMAKE_MSVC_RUNTIME_CHECKS: ""
    buildResult:
      variable: "COMPILER_SUPPORTS_SSE4_2"
      cached: true
      stdout: |
        Change Dir: 'D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-qc0mt2'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_996b6.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 8/11/2025 12:46:26 AM.
        
        Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-qc0mt2\\cmTC_996b6.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_996b6.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-qc0mt2\\Debug\\".
          Creating directory "cmTC_996b6.dir\\Debug\\cmTC_996b6.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_996b6.dir\\Debug\\cmTC_996b6.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_996b6.dir\\Debug\\cmTC_996b6.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _GNU_SOURCE=1 /D COMPILER_SUPPORTS_SSE4_2 /D "CMAKE_INTDIR=\\"Debug\\"" /MDd /GS /fp:precise /Fo"cmTC_996b6.dir\\Debug\\\\" /Fd"cmTC_996b6.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-qc0mt2\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34809 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _GNU_SOURCE=1 /D COMPILER_SUPPORTS_SSE4_2 /D "CMAKE_INTDIR=\\"Debug\\"" /MDd /GS /fp:precise /Fo"cmTC_996b6.dir\\Debug\\\\" /Fd"cmTC_996b6.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-qc0mt2\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /OUT:"D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-qc0mt2\\Debug\\cmTC_996b6.exe" /INCREMENTAL /ILK:"cmTC_996b6.dir\\Debug\\cmTC_996b6.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-qc0mt2/Debug/cmTC_996b6.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-qc0mt2/Debug/cmTC_996b6.lib" /MACHINE:X64  /machine:x64 cmTC_996b6.dir\\Debug\\src.obj
          cmTC_996b6.vcxproj -> D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-qc0mt2\\Debug\\cmTC_996b6.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_996b6.dir\\Debug\\cmTC_996b6.tlog\\unsuccessfulbuild".
          Touching "cmTC_996b6.dir\\Debug\\cmTC_996b6.tlog\\cmTC_996b6.lastbuildstate".
        Done Building Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-qc0mt2\\cmTC_996b6.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.68
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CheckCSourceCompiles.cmake:103 (cmake_check_source_compiles)"
      - "libs/SDL3/CMakeLists.txt:811 (check_c_source_compiles)"
    checks:
      - "Performing Test COMPILER_SUPPORTS_AVX"
    directories:
      source: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-meet1m"
      binary: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-meet1m"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od "
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/Frank Work All/CPP/f0/libs/SDL3/cmake"
      CMAKE_MSVC_RUNTIME_CHECKS: ""
    buildResult:
      variable: "COMPILER_SUPPORTS_AVX"
      cached: true
      stdout: |
        Change Dir: 'D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-meet1m'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_76db4.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 8/11/2025 12:46:27 AM.
        
        Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-meet1m\\cmTC_76db4.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_76db4.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-meet1m\\Debug\\".
          Creating directory "cmTC_76db4.dir\\Debug\\cmTC_76db4.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_76db4.dir\\Debug\\cmTC_76db4.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_76db4.dir\\Debug\\cmTC_76db4.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _GNU_SOURCE=1 /D COMPILER_SUPPORTS_AVX /D "CMAKE_INTDIR=\\"Debug\\"" /MDd /GS /fp:precise /Fo"cmTC_76db4.dir\\Debug\\\\" /Fd"cmTC_76db4.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-meet1m\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34809 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _GNU_SOURCE=1 /D COMPILER_SUPPORTS_AVX /D "CMAKE_INTDIR=\\"Debug\\"" /MDd /GS /fp:precise /Fo"cmTC_76db4.dir\\Debug\\\\" /Fd"cmTC_76db4.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-meet1m\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /OUT:"D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-meet1m\\Debug\\cmTC_76db4.exe" /INCREMENTAL /ILK:"cmTC_76db4.dir\\Debug\\cmTC_76db4.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-meet1m/Debug/cmTC_76db4.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-meet1m/Debug/cmTC_76db4.lib" /MACHINE:X64  /machine:x64 cmTC_76db4.dir\\Debug\\src.obj
          cmTC_76db4.vcxproj -> D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-meet1m\\Debug\\cmTC_76db4.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_76db4.dir\\Debug\\cmTC_76db4.tlog\\unsuccessfulbuild".
          Touching "cmTC_76db4.dir\\Debug\\cmTC_76db4.tlog\\cmTC_76db4.lastbuildstate".
        Done Building Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-meet1m\\cmTC_76db4.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.56
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CheckCSourceCompiles.cmake:103 (cmake_check_source_compiles)"
      - "libs/SDL3/CMakeLists.txt:832 (check_c_source_compiles)"
    checks:
      - "Performing Test COMPILER_SUPPORTS_AVX2"
    directories:
      source: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-3gh8fd"
      binary: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-3gh8fd"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od "
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/Frank Work All/CPP/f0/libs/SDL3/cmake"
      CMAKE_MSVC_RUNTIME_CHECKS: ""
    buildResult:
      variable: "COMPILER_SUPPORTS_AVX2"
      cached: true
      stdout: |
        Change Dir: 'D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-3gh8fd'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_d7f0b.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 8/11/2025 12:46:28 AM.
        
        Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3gh8fd\\cmTC_d7f0b.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_d7f0b.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3gh8fd\\Debug\\".
          Creating directory "cmTC_d7f0b.dir\\Debug\\cmTC_d7f0b.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_d7f0b.dir\\Debug\\cmTC_d7f0b.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_d7f0b.dir\\Debug\\cmTC_d7f0b.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _GNU_SOURCE=1 /D COMPILER_SUPPORTS_AVX2 /D "CMAKE_INTDIR=\\"Debug\\"" /MDd /GS /fp:precise /Fo"cmTC_d7f0b.dir\\Debug\\\\" /Fd"cmTC_d7f0b.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3gh8fd\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34809 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _GNU_SOURCE=1 /D COMPILER_SUPPORTS_AVX2 /D "CMAKE_INTDIR=\\"Debug\\"" /MDd /GS /fp:precise /Fo"cmTC_d7f0b.dir\\Debug\\\\" /Fd"cmTC_d7f0b.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3gh8fd\\src.c"
          src.c
        D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3gh8fd\\src.c(1,1): error C1090: PDB API call failed, error code '3': D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3gh8fd\\cmTC_d7f0b.dir\\Debug\\vc143.pdb [D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3gh8fd\\cmTC_d7f0b.vcxproj]
        Done Building Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3gh8fd\\cmTC_d7f0b.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3gh8fd\\cmTC_d7f0b.vcxproj" (default target) (1) ->
        (ClCompile target) -> 
          D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3gh8fd\\src.c(1,1): error C1090: PDB API call failed, error code '3': D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3gh8fd\\cmTC_d7f0b.dir\\Debug\\vc143.pdb [D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3gh8fd\\cmTC_d7f0b.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.38
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CheckCSourceCompiles.cmake:103 (cmake_check_source_compiles)"
      - "libs/SDL3/CMakeLists.txt:853 (check_c_source_compiles)"
    checks:
      - "Performing Test COMPILER_SUPPORTS_AVX512F"
    directories:
      source: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-6gmyfy"
      binary: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-6gmyfy"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od "
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/Frank Work All/CPP/f0/libs/SDL3/cmake"
      CMAKE_MSVC_RUNTIME_CHECKS: ""
    buildResult:
      variable: "COMPILER_SUPPORTS_AVX512F"
      cached: true
      stdout: |
        Change Dir: 'D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-6gmyfy'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_9c794.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 8/11/2025 12:46:28 AM.
        
        Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6gmyfy\\cmTC_9c794.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_9c794.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6gmyfy\\Debug\\".
          Creating directory "cmTC_9c794.dir\\Debug\\cmTC_9c794.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_9c794.dir\\Debug\\cmTC_9c794.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_9c794.dir\\Debug\\cmTC_9c794.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _GNU_SOURCE=1 /D COMPILER_SUPPORTS_AVX512F /D "CMAKE_INTDIR=\\"Debug\\"" /MDd /GS /fp:precise /Fo"cmTC_9c794.dir\\Debug\\\\" /Fd"cmTC_9c794.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6gmyfy\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34809 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _GNU_SOURCE=1 /D COMPILER_SUPPORTS_AVX512F /D "CMAKE_INTDIR=\\"Debug\\"" /MDd /GS /fp:precise /Fo"cmTC_9c794.dir\\Debug\\\\" /Fd"cmTC_9c794.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6gmyfy\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /OUT:"D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6gmyfy\\Debug\\cmTC_9c794.exe" /INCREMENTAL /ILK:"cmTC_9c794.dir\\Debug\\cmTC_9c794.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-6gmyfy/Debug/cmTC_9c794.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-6gmyfy/Debug/cmTC_9c794.lib" /MACHINE:X64  /machine:x64 cmTC_9c794.dir\\Debug\\src.obj
          cmTC_9c794.vcxproj -> D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6gmyfy\\Debug\\cmTC_9c794.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_9c794.dir\\Debug\\cmTC_9c794.tlog\\unsuccessfulbuild".
          Touching "cmTC_9c794.dir\\Debug\\cmTC_9c794.tlog\\cmTC_9c794.lastbuildstate".
        Done Building Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6gmyfy\\cmTC_9c794.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.55
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CheckIncludeFile.cmake:165 (try_compile)"
      - "libs/SDL3/CMakeLists.txt:996 (check_include_file)"
    checks:
      - "Looking for malloc.h"
    directories:
      source: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-ydxfzr"
      binary: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-ydxfzr"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od "
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/Frank Work All/CPP/f0/libs/SDL3/cmake"
      CMAKE_MSVC_RUNTIME_CHECKS: ""
    buildResult:
      variable: "HAVE_MALLOC_H"
      cached: true
      stdout: |
        Change Dir: 'D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-ydxfzr'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_b61ec.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 8/11/2025 12:46:29 AM.
        
        Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ydxfzr\\cmTC_b61ec.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_b61ec.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ydxfzr\\Debug\\".
          Creating directory "cmTC_b61ec.dir\\Debug\\cmTC_b61ec.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_b61ec.dir\\Debug\\cmTC_b61ec.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_b61ec.dir\\Debug\\cmTC_b61ec.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _GNU_SOURCE=1 /D "CMAKE_INTDIR=\\"Debug\\"" /MDd /GS /fp:precise /Fo"cmTC_b61ec.dir\\Debug\\\\" /Fd"cmTC_b61ec.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ydxfzr\\CheckIncludeFile.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34809 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _GNU_SOURCE=1 /D "CMAKE_INTDIR=\\"Debug\\"" /MDd /GS /fp:precise /Fo"cmTC_b61ec.dir\\Debug\\\\" /Fd"cmTC_b61ec.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ydxfzr\\CheckIncludeFile.c"
          CheckIncludeFile.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /OUT:"D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ydxfzr\\Debug\\cmTC_b61ec.exe" /INCREMENTAL /ILK:"cmTC_b61ec.dir\\Debug\\cmTC_b61ec.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-ydxfzr/Debug/cmTC_b61ec.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-ydxfzr/Debug/cmTC_b61ec.lib" /MACHINE:X64  /machine:x64 cmTC_b61ec.dir\\Debug\\CheckIncludeFile.obj
          cmTC_b61ec.vcxproj -> D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ydxfzr\\Debug\\cmTC_b61ec.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_b61ec.dir\\Debug\\cmTC_b61ec.tlog\\unsuccessfulbuild".
          Touching "cmTC_b61ec.dir\\Debug\\cmTC_b61ec.tlog\\cmTC_b61ec.lastbuildstate".
        Done Building Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ydxfzr\\cmTC_b61ec.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.56
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CheckSymbolExists.cmake:193 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CheckSymbolExists.cmake:98 (__CHECK_SYMBOL_EXISTS_IMPL)"
      - "libs/SDL3/CMakeLists.txt:1077 (check_symbol_exists)"
    checks:
      - "Looking for _copysign"
    directories:
      source: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-g3wvkg"
      binary: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-g3wvkg"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od "
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/Frank Work All/CPP/f0/libs/SDL3/cmake"
      CMAKE_MSVC_RUNTIME_CHECKS: ""
    buildResult:
      variable: "LIBC_HAS__COPYSIGN"
      cached: true
      stdout: |
        Change Dir: 'D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-g3wvkg'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_0b013.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 8/11/2025 12:46:30 AM.
        
        Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-g3wvkg\\cmTC_0b013.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_0b013.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-g3wvkg\\Debug\\".
          Creating directory "cmTC_0b013.dir\\Debug\\cmTC_0b013.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_0b013.dir\\Debug\\cmTC_0b013.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_0b013.dir\\Debug\\cmTC_0b013.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _GNU_SOURCE=1 /D "CMAKE_INTDIR=\\"Debug\\"" /MDd /GS /fp:precise /Fo"cmTC_0b013.dir\\Debug\\\\" /Fd"cmTC_0b013.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-g3wvkg\\CheckSymbolExists.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34809 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _GNU_SOURCE=1 /D "CMAKE_INTDIR=\\"Debug\\"" /MDd /GS /fp:precise /Fo"cmTC_0b013.dir\\Debug\\\\" /Fd"cmTC_0b013.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-g3wvkg\\CheckSymbolExists.c"
          CheckSymbolExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /OUT:"D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-g3wvkg\\Debug\\cmTC_0b013.exe" /INCREMENTAL /ILK:"cmTC_0b013.dir\\Debug\\cmTC_0b013.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-g3wvkg/Debug/cmTC_0b013.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-g3wvkg/Debug/cmTC_0b013.lib" /MACHINE:X64  /machine:x64 cmTC_0b013.dir\\Debug\\CheckSymbolExists.obj
          cmTC_0b013.vcxproj -> D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-g3wvkg\\Debug\\cmTC_0b013.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_0b013.dir\\Debug\\cmTC_0b013.tlog\\unsuccessfulbuild".
          Touching "cmTC_0b013.dir\\Debug\\cmTC_0b013.tlog\\cmTC_0b013.lastbuildstate".
        Done Building Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-g3wvkg\\cmTC_0b013.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:01.13
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CheckSymbolExists.cmake:193 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CheckSymbolExists.cmake:98 (__CHECK_SYMBOL_EXISTS_IMPL)"
      - "libs/SDL3/CMakeLists.txt:1077 (check_symbol_exists)"
    checks:
      - "Looking for _fseeki64"
    directories:
      source: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-a4gf95"
      binary: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-a4gf95"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od "
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/Frank Work All/CPP/f0/libs/SDL3/cmake"
      CMAKE_MSVC_RUNTIME_CHECKS: ""
    buildResult:
      variable: "LIBC_HAS__FSEEKI64"
      cached: true
      stdout: |
        Change Dir: 'D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-a4gf95'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_ca087.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 8/11/2025 12:46:31 AM.
        
        Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-a4gf95\\cmTC_ca087.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_ca087.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-a4gf95\\Debug\\".
          Creating directory "cmTC_ca087.dir\\Debug\\cmTC_ca087.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_ca087.dir\\Debug\\cmTC_ca087.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_ca087.dir\\Debug\\cmTC_ca087.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _GNU_SOURCE=1 /D "CMAKE_INTDIR=\\"Debug\\"" /MDd /GS /fp:precise /Fo"cmTC_ca087.dir\\Debug\\\\" /Fd"cmTC_ca087.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-a4gf95\\CheckSymbolExists.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34809 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _GNU_SOURCE=1 /D "CMAKE_INTDIR=\\"Debug\\"" /MDd /GS /fp:precise /Fo"cmTC_ca087.dir\\Debug\\\\" /Fd"cmTC_ca087.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-a4gf95\\CheckSymbolExists.c"
          CheckSymbolExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /OUT:"D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-a4gf95\\Debug\\cmTC_ca087.exe" /INCREMENTAL /ILK:"cmTC_ca087.dir\\Debug\\cmTC_ca087.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-a4gf95/Debug/cmTC_ca087.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-a4gf95/Debug/cmTC_ca087.lib" /MACHINE:X64  /machine:x64 cmTC_ca087.dir\\Debug\\CheckSymbolExists.obj
          cmTC_ca087.vcxproj -> D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-a4gf95\\Debug\\cmTC_ca087.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_ca087.dir\\Debug\\cmTC_ca087.tlog\\unsuccessfulbuild".
          Touching "cmTC_ca087.dir\\Debug\\cmTC_ca087.tlog\\cmTC_ca087.lastbuildstate".
        Done Building Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-a4gf95\\cmTC_ca087.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:01.04
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CheckCSourceCompiles.cmake:103 (cmake_check_source_compiles)"
      - "libs/SDL3/CMakeLists.txt:2001 (check_c_source_compiles)"
    checks:
      - "Performing Test HAVE_GAMEINPUT_H"
    directories:
      source: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-klh5bz"
      binary: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-klh5bz"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od "
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/Frank Work All/CPP/f0/libs/SDL3/cmake"
      CMAKE_MSVC_RUNTIME_CHECKS: ""
    buildResult:
      variable: "HAVE_GAMEINPUT_H"
      cached: true
      stdout: |
        Change Dir: 'D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-klh5bz'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_6ffd7.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 8/11/2025 12:46:32 AM.
        
        Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-klh5bz\\cmTC_6ffd7.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_6ffd7.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-klh5bz\\Debug\\".
          Creating directory "cmTC_6ffd7.dir\\Debug\\cmTC_6ffd7.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_6ffd7.dir\\Debug\\cmTC_6ffd7.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_6ffd7.dir\\Debug\\cmTC_6ffd7.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _GNU_SOURCE=1 /D HAVE_GAMEINPUT_H /D "CMAKE_INTDIR=\\"Debug\\"" /MDd /GS /fp:precise /Fo"cmTC_6ffd7.dir\\Debug\\\\" /Fd"cmTC_6ffd7.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-klh5bz\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34809 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _GNU_SOURCE=1 /D HAVE_GAMEINPUT_H /D "CMAKE_INTDIR=\\"Debug\\"" /MDd /GS /fp:precise /Fo"cmTC_6ffd7.dir\\Debug\\\\" /Fd"cmTC_6ffd7.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-klh5bz\\src.c"
          src.c
        D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-klh5bz\\src.c(4,14): error C1083: Cannot open include file: 'gameinput.h': No such file or directory [D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-klh5bz\\cmTC_6ffd7.vcxproj]
        Done Building Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-klh5bz\\cmTC_6ffd7.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-klh5bz\\cmTC_6ffd7.vcxproj" (default target) (1) ->
        (ClCompile target) -> 
          D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-klh5bz\\src.c(4,14): error C1083: Cannot open include file: 'gameinput.h': No such file or directory [D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-klh5bz\\cmTC_6ffd7.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.35
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CheckIncludeFile.cmake:165 (try_compile)"
      - "libs/SDL3/CMakeLists.txt:2007 (check_include_file)"
    checks:
      - "Looking for dxgi1_6.h"
    directories:
      source: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-ge5qf4"
      binary: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-ge5qf4"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od "
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/Frank Work All/CPP/f0/libs/SDL3/cmake"
      CMAKE_MSVC_RUNTIME_CHECKS: ""
    buildResult:
      variable: "HAVE_DXGI1_6_H"
      cached: true
      stdout: |
        Change Dir: 'D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-ge5qf4'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_82248.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 8/11/2025 12:46:33 AM.
        
        Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ge5qf4\\cmTC_82248.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_82248.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ge5qf4\\Debug\\".
          Creating directory "cmTC_82248.dir\\Debug\\cmTC_82248.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_82248.dir\\Debug\\cmTC_82248.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_82248.dir\\Debug\\cmTC_82248.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _GNU_SOURCE=1 /D "CMAKE_INTDIR=\\"Debug\\"" /MDd /GS /fp:precise /Fo"cmTC_82248.dir\\Debug\\\\" /Fd"cmTC_82248.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ge5qf4\\CheckIncludeFile.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34809 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _GNU_SOURCE=1 /D "CMAKE_INTDIR=\\"Debug\\"" /MDd /GS /fp:precise /Fo"cmTC_82248.dir\\Debug\\\\" /Fd"cmTC_82248.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ge5qf4\\CheckIncludeFile.c"
          CheckIncludeFile.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /OUT:"D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ge5qf4\\Debug\\cmTC_82248.exe" /INCREMENTAL /ILK:"cmTC_82248.dir\\Debug\\cmTC_82248.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-ge5qf4/Debug/cmTC_82248.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-ge5qf4/Debug/cmTC_82248.lib" /MACHINE:X64  /machine:x64 cmTC_82248.dir\\Debug\\CheckIncludeFile.obj
          cmTC_82248.vcxproj -> D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ge5qf4\\Debug\\cmTC_82248.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_82248.dir\\Debug\\cmTC_82248.tlog\\unsuccessfulbuild".
          Touching "cmTC_82248.dir\\Debug\\cmTC_82248.tlog\\cmTC_82248.lastbuildstate".
        Done Building Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ge5qf4\\cmTC_82248.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.78
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CheckCSourceCompiles.cmake:103 (cmake_check_source_compiles)"
      - "libs/SDL3/CMakeLists.txt:2014 (check_c_source_compiles)"
    checks:
      - "Performing Test HAVE_MFAPI_H"
    directories:
      source: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-8rn79o"
      binary: "D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-8rn79o"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od "
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/Frank Work All/CPP/f0/libs/SDL3/cmake"
      CMAKE_MSVC_RUNTIME_CHECKS: ""
    buildResult:
      variable: "HAVE_MFAPI_H"
      cached: true
      stdout: |
        Change Dir: 'D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-8rn79o'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_e4fd8.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 8/11/2025 12:46:34 AM.
        
        Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8rn79o\\cmTC_e4fd8.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_e4fd8.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8rn79o\\Debug\\".
          Creating directory "cmTC_e4fd8.dir\\Debug\\cmTC_e4fd8.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_e4fd8.dir\\Debug\\cmTC_e4fd8.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_e4fd8.dir\\Debug\\cmTC_e4fd8.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _GNU_SOURCE=1 /D HAVE_MFAPI_H /D "CMAKE_INTDIR=\\"Debug\\"" /MDd /GS /fp:precise /Fo"cmTC_e4fd8.dir\\Debug\\\\" /Fd"cmTC_e4fd8.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8rn79o\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34809 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _GNU_SOURCE=1 /D HAVE_MFAPI_H /D "CMAKE_INTDIR=\\"Debug\\"" /MDd /GS /fp:precise /Fo"cmTC_e4fd8.dir\\Debug\\\\" /Fd"cmTC_e4fd8.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8rn79o\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /OUT:"D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8rn79o\\Debug\\cmTC_e4fd8.exe" /INCREMENTAL /ILK:"cmTC_e4fd8.dir\\Debug\\cmTC_e4fd8.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-8rn79o/Debug/cmTC_e4fd8.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"D:/Frank Work All/CPP/f0/build/CMakeFiles/CMakeScratch/TryCompile-8rn79o/Debug/cmTC_e4fd8.lib" /MACHINE:X64  /machine:x64 cmTC_e4fd8.dir\\Debug\\src.obj
          cmTC_e4fd8.vcxproj -> D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8rn79o\\Debug\\cmTC_e4fd8.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_e4fd8.dir\\Debug\\cmTC_e4fd8.tlog\\unsuccessfulbuild".
          Touching "cmTC_e4fd8.dir\\Debug\\cmTC_e4fd8.tlog\\cmTC_e4fd8.lastbuildstate".
        Done Building Project "D:\\Frank Work All\\CPP\\f0\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8rn79o\\cmTC_e4fd8.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.82
        
      exitCode: 0
  -
    kind: "find-v1"
    backtrace:
      - "libs/SDL3/cmake/FindLibUSB.cmake:12 (find_library)"
      - "libs/SDL3/cmake/sdlchecks.cmake:1137 (find_package)"
      - "libs/SDL3/CMakeLists.txt:2150 (CheckHIDAPI)"
    mode: "library"
    variable: "LibUSB_LIBRARY"
    description: "Path to a library."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "usb-1.0"
      - "libusb-1.0"
    candidate_directories:
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Users/<USER>/scoop/shims/"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ProgramData/mingw64/mingw64/bin/"
      - "C:/Program Files/lib/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/lib/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/lib/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/MySDL3Game/lib/"
      - "C:/Program Files (x86)/MySDL3Game/"
      - "C:/Program Files (x86)/MySDL3Game/bin/"
      - "/bin/"
    searched_directories:
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Users/<USER>/scoop/shims/"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ProgramData/mingw64/mingw64/bin/"
      - "C:/Program Files/lib/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/lib/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/lib/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/MySDL3Game/lib/"
      - "C:/Program Files (x86)/MySDL3Game/"
      - "C:/Program Files (x86)/MySDL3Game/bin/"
      - "/bin/"
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Users/<USER>/scoop/shims/"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ProgramData/mingw64/mingw64/bin/"
      - "C:/Program Files/lib/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/lib/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/lib/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/MySDL3Game/lib/"
      - "C:/Program Files (x86)/MySDL3Game/"
      - "C:/Program Files (x86)/MySDL3Game/bin/"
      - "/bin/"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Users\\<USER>\\scoop\\shims"
        - "C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ProgramData\\mingw64\\mingw64\\bin"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/MySDL3Game"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/MySDL3Game"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "C:/Program Files (x86)/MySDL3Game/bin"
        - "C:/Program Files/CMake/bin"
        - "/bin"
  -
    kind: "find-v1"
    backtrace:
      - "libs/SDL3/cmake/FindLibUSB.cmake:17 (find_path)"
      - "libs/SDL3/cmake/sdlchecks.cmake:1137 (find_package)"
      - "libs/SDL3/CMakeLists.txt:2150 (CheckHIDAPI)"
    mode: "path"
    variable: "LibUSB_INCLUDE_PATH"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "libusb.h"
    candidate_directories:
      - "C:/Program Files/Oculus/Support/oculus-runtime/libusb-1.0/"
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/libusb-1.0/"
      - "C:/Windows/System32/"
      - "C:/Windows/libusb-1.0/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/libusb-1.0/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/libusb-1.0/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/libusb-1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/libusb-1.0/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files/dotnet/libusb-1.0/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/Git/cmd/libusb-1.0/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/nodejs/libusb-1.0/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/libusb-1.0/"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/"
      - "C:/Program Files/Docker/Docker/resources/bin/libusb-1.0/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/libusb-1.0/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/libusb-1.0/"
      - "C:/Program Files/CMake/bin/"
      - "C:/ProgramData/chocolatey/bin/libusb-1.0/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Users/<USER>/scoop/shims/libusb-1.0/"
      - "C:/Users/<USER>/scoop/shims/"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/libusb-1.0/"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/libusb-1.0/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/.dotnet/tools/libusb-1.0/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/libusb-1.0/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/libusb-1.0/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/libusb-1.0/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ProgramData/mingw64/mingw64/bin/libusb-1.0/"
      - "C:/ProgramData/mingw64/mingw64/bin/"
      - "C:/Program Files/include/libusb-1.0/"
      - "C:/Program Files/include/"
      - "C:/Program Files/libusb-1.0/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/include/libusb-1.0/"
      - "C:/Program Files (x86)/include/"
      - "C:/Program Files (x86)/libusb-1.0/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/include/libusb-1.0/"
      - "C:/Program Files/CMake/include/"
      - "C:/Program Files/CMake/libusb-1.0/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/MySDL3Game/include/libusb-1.0/"
      - "C:/Program Files (x86)/MySDL3Game/include/"
      - "C:/Program Files (x86)/MySDL3Game/libusb-1.0/"
      - "C:/Program Files (x86)/MySDL3Game/"
    searched_directories:
      - "C:/Program Files/Oculus/Support/oculus-runtime/libusb-1.0/libusb.h"
      - "C:/Program Files/Oculus/Support/oculus-runtime/libusb.h"
      - "C:/Windows/System32/libusb-1.0/libusb.h"
      - "C:/Windows/System32/libusb.h"
      - "C:/Windows/libusb-1.0/libusb.h"
      - "C:/Windows/libusb.h"
      - "C:/Windows/System32/wbem/libusb-1.0/libusb.h"
      - "C:/Windows/System32/wbem/libusb.h"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/libusb-1.0/libusb.h"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/libusb.h"
      - "C:/Windows/System32/OpenSSH/libusb-1.0/libusb.h"
      - "C:/Windows/System32/OpenSSH/libusb.h"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/libusb-1.0/libusb.h"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/libusb.h"
      - "C:/Program Files/dotnet/libusb-1.0/libusb.h"
      - "C:/Program Files/dotnet/libusb.h"
      - "C:/Program Files/Git/cmd/libusb-1.0/libusb.h"
      - "C:/Program Files/Git/cmd/libusb.h"
      - "C:/Program Files/nodejs/libusb-1.0/libusb.h"
      - "C:/Program Files/nodejs/libusb.h"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/libusb-1.0/libusb.h"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/libusb.h"
      - "C:/Program Files/Docker/Docker/resources/bin/libusb-1.0/libusb.h"
      - "C:/Program Files/Docker/Docker/resources/bin/libusb.h"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/libusb-1.0/libusb.h"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/libusb.h"
      - "C:/Program Files/CMake/bin/libusb-1.0/libusb.h"
      - "C:/Program Files/CMake/bin/libusb.h"
      - "C:/ProgramData/chocolatey/bin/libusb-1.0/libusb.h"
      - "C:/ProgramData/chocolatey/bin/libusb.h"
      - "C:/Users/<USER>/scoop/shims/libusb-1.0/libusb.h"
      - "C:/Users/<USER>/scoop/shims/libusb.h"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/libusb-1.0/libusb.h"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/libusb.h"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/libusb-1.0/libusb.h"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/libusb.h"
      - "C:/Users/<USER>/.dotnet/tools/libusb-1.0/libusb.h"
      - "C:/Users/<USER>/.dotnet/tools/libusb.h"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/libusb-1.0/libusb.h"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/libusb.h"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/libusb-1.0/libusb.h"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/libusb.h"
      - "C:/Users/<USER>/AppData/Roaming/npm/libusb-1.0/libusb.h"
      - "C:/Users/<USER>/AppData/Roaming/npm/libusb.h"
      - "C:/ProgramData/mingw64/mingw64/bin/libusb-1.0/libusb.h"
      - "C:/ProgramData/mingw64/mingw64/bin/libusb.h"
      - "C:/Program Files/include/libusb-1.0/libusb.h"
      - "C:/Program Files/include/libusb.h"
      - "C:/Program Files/libusb-1.0/libusb.h"
      - "C:/Program Files/libusb.h"
      - "C:/Program Files (x86)/include/libusb-1.0/libusb.h"
      - "C:/Program Files (x86)/include/libusb.h"
      - "C:/Program Files (x86)/libusb-1.0/libusb.h"
      - "C:/Program Files (x86)/libusb.h"
      - "C:/Program Files/CMake/include/libusb-1.0/libusb.h"
      - "C:/Program Files/CMake/include/libusb.h"
      - "C:/Program Files/CMake/libusb-1.0/libusb.h"
      - "C:/Program Files/CMake/libusb.h"
      - "C:/Program Files (x86)/MySDL3Game/include/libusb-1.0/libusb.h"
      - "C:/Program Files (x86)/MySDL3Game/include/libusb.h"
      - "C:/Program Files (x86)/MySDL3Game/libusb-1.0/libusb.h"
      - "C:/Program Files (x86)/MySDL3Game/libusb.h"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Users\\<USER>\\scoop\\shims"
        - "C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ProgramData\\mingw64\\mingw64\\bin"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/MySDL3Game"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/MySDL3Game"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/FindGit.cmake:86 (find_program)"
      - "libs/SDL3/cmake/GetGitRevisionDescription.cmake:177 (find_package)"
      - "libs/SDL3/CMakeLists.txt:3262 (git_describe)"
    mode: "program"
    variable: "GIT_EXECUTABLE"
    description: "Git command line client"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "git.cmd"
      - "git"
    candidate_directories:
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Users/<USER>/scoop/shims/"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ProgramData/mingw64/mingw64/bin/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/MySDL3Game/bin/"
      - "C:/Program Files (x86)/MySDL3Game/sbin/"
      - "C:/Program Files (x86)/MySDL3Game/"
      - "C:/Users/<USER>/AppData/Local/Atlassian/SourceTree/git_local/bin/"
    searched_directories:
      - "C:/Program Files/Oculus/Support/oculus-runtime/git.cmd.com"
      - "C:/Program Files/Oculus/Support/oculus-runtime/git.cmd.exe"
      - "C:/Program Files/Oculus/Support/oculus-runtime/git.cmd"
      - "C:/Windows/System32/git.cmd.com"
      - "C:/Windows/System32/git.cmd.exe"
      - "C:/Windows/System32/git.cmd"
      - "C:/Windows/git.cmd.com"
      - "C:/Windows/git.cmd.exe"
      - "C:/Windows/git.cmd"
      - "C:/Windows/System32/wbem/git.cmd.com"
      - "C:/Windows/System32/wbem/git.cmd.exe"
      - "C:/Windows/System32/wbem/git.cmd"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/git.cmd.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/git.cmd.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/git.cmd"
      - "C:/Windows/System32/OpenSSH/git.cmd.com"
      - "C:/Windows/System32/OpenSSH/git.cmd.exe"
      - "C:/Windows/System32/OpenSSH/git.cmd"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/git.cmd.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/git.cmd.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/git.cmd"
      - "C:/Program Files/dotnet/git.cmd.com"
      - "C:/Program Files/dotnet/git.cmd.exe"
      - "C:/Program Files/dotnet/git.cmd"
      - "C:/Program Files/Git/cmd/git.cmd.com"
      - "C:/Program Files/Git/cmd/git.cmd.exe"
      - "C:/Program Files/Git/cmd/git.cmd"
      - "C:/Program Files/nodejs/git.cmd.com"
      - "C:/Program Files/nodejs/git.cmd.exe"
      - "C:/Program Files/nodejs/git.cmd"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/git.cmd.com"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/git.cmd.exe"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/git.cmd"
      - "C:/Program Files/Docker/Docker/resources/bin/git.cmd.com"
      - "C:/Program Files/Docker/Docker/resources/bin/git.cmd.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/git.cmd"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/git.cmd.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/git.cmd.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/git.cmd"
      - "C:/Program Files/CMake/bin/git.cmd.com"
      - "C:/Program Files/CMake/bin/git.cmd.exe"
      - "C:/Program Files/CMake/bin/git.cmd"
      - "C:/ProgramData/chocolatey/bin/git.cmd.com"
      - "C:/ProgramData/chocolatey/bin/git.cmd.exe"
      - "C:/ProgramData/chocolatey/bin/git.cmd"
      - "C:/Users/<USER>/scoop/shims/git.cmd.com"
      - "C:/Users/<USER>/scoop/shims/git.cmd.exe"
      - "C:/Users/<USER>/scoop/shims/git.cmd"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/git.cmd.com"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/git.cmd.exe"
      - "C:/Program Files/MySQL/MySQL Shell 8.0/bin/git.cmd"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/git.cmd.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/git.cmd.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/git.cmd"
      - "C:/Users/<USER>/.dotnet/tools/git.cmd.com"
      - "C:/Users/<USER>/.dotnet/tools/git.cmd.exe"
      - "C:/Users/<USER>/.dotnet/tools/git.cmd"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/git.cmd.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/git.cmd.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/git.cmd"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/git.cmd.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/git.cmd.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/git.cmd"
      - "C:/Users/<USER>/AppData/Roaming/npm/git.cmd.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/git.cmd.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/git.cmd"
      - "C:/ProgramData/mingw64/mingw64/bin/git.cmd.com"
      - "C:/ProgramData/mingw64/mingw64/bin/git.cmd.exe"
      - "C:/ProgramData/mingw64/mingw64/bin/git.cmd"
      - "C:/Program Files/bin/git.cmd.com"
      - "C:/Program Files/bin/git.cmd.exe"
      - "C:/Program Files/bin/git.cmd"
      - "C:/Program Files/sbin/git.cmd.com"
      - "C:/Program Files/sbin/git.cmd.exe"
      - "C:/Program Files/sbin/git.cmd"
      - "C:/Program Files/git.cmd.com"
      - "C:/Program Files/git.cmd.exe"
      - "C:/Program Files/git.cmd"
      - "C:/Program Files (x86)/bin/git.cmd.com"
      - "C:/Program Files (x86)/bin/git.cmd.exe"
      - "C:/Program Files (x86)/bin/git.cmd"
      - "C:/Program Files (x86)/sbin/git.cmd.com"
      - "C:/Program Files (x86)/sbin/git.cmd.exe"
      - "C:/Program Files (x86)/sbin/git.cmd"
      - "C:/Program Files (x86)/git.cmd.com"
      - "C:/Program Files (x86)/git.cmd.exe"
      - "C:/Program Files (x86)/git.cmd"
      - "C:/Program Files/CMake/bin/git.cmd.com"
      - "C:/Program Files/CMake/bin/git.cmd.exe"
      - "C:/Program Files/CMake/bin/git.cmd"
      - "C:/Program Files/CMake/sbin/git.cmd.com"
      - "C:/Program Files/CMake/sbin/git.cmd.exe"
      - "C:/Program Files/CMake/sbin/git.cmd"
      - "C:/Program Files/CMake/git.cmd.com"
      - "C:/Program Files/CMake/git.cmd.exe"
      - "C:/Program Files/CMake/git.cmd"
      - "C:/Program Files (x86)/MySDL3Game/bin/git.cmd.com"
      - "C:/Program Files (x86)/MySDL3Game/bin/git.cmd.exe"
      - "C:/Program Files (x86)/MySDL3Game/bin/git.cmd"
      - "C:/Program Files (x86)/MySDL3Game/sbin/git.cmd.com"
      - "C:/Program Files (x86)/MySDL3Game/sbin/git.cmd.exe"
      - "C:/Program Files (x86)/MySDL3Game/sbin/git.cmd"
      - "C:/Program Files (x86)/MySDL3Game/git.cmd.com"
      - "C:/Program Files (x86)/MySDL3Game/git.cmd.exe"
      - "C:/Program Files (x86)/MySDL3Game/git.cmd"
      - "C:/Users/<USER>/AppData/Local/Atlassian/SourceTree/git_local/bin/git.cmd.com"
      - "C:/Users/<USER>/AppData/Local/Atlassian/SourceTree/git_local/bin/git.cmd.exe"
      - "C:/Users/<USER>/AppData/Local/Atlassian/SourceTree/git_local/bin/git.cmd"
      - "C:/Program Files/Oculus/Support/oculus-runtime/git.com"
      - "C:/Program Files/Oculus/Support/oculus-runtime/git.exe"
      - "C:/Program Files/Oculus/Support/oculus-runtime/git"
      - "C:/Windows/System32/git.com"
      - "C:/Windows/System32/git.exe"
      - "C:/Windows/System32/git"
      - "C:/Windows/git.com"
      - "C:/Windows/git.exe"
      - "C:/Windows/git"
      - "C:/Windows/System32/wbem/git.com"
      - "C:/Windows/System32/wbem/git.exe"
      - "C:/Windows/System32/wbem/git"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/git.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/git.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/git"
      - "C:/Windows/System32/OpenSSH/git.com"
      - "C:/Windows/System32/OpenSSH/git.exe"
      - "C:/Windows/System32/OpenSSH/git"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/git.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/git.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/git"
      - "C:/Program Files/dotnet/git.com"
      - "C:/Program Files/dotnet/git.exe"
      - "C:/Program Files/dotnet/git"
      - "C:/Program Files/Git/cmd/git.com"
    found: "C:/Program Files/Git/cmd/git.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Users\\<USER>\\scoop\\shims"
        - "C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ProgramData\\mingw64\\mingw64\\bin"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/MySDL3Game"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/MySDL3Game"
...
