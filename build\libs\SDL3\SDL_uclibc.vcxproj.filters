﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\libm\e_atan2.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\libm\e_exp.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\libm\e_fmod.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\libm\e_log.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\libm\e_log10.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\libm\e_pow.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\libm\e_rem_pio2.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\libm\e_sqrt.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\libm\k_cos.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\libm\k_rem_pio2.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\libm\k_sin.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\libm\k_tan.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\libm\s_atan.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\libm\s_copysign.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\libm\s_cos.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\libm\s_fabs.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\libm\s_floor.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\libm\s_isinf.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\libm\s_isinff.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\libm\s_isnan.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\libm\s_isnanf.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\libm\s_modf.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\libm\s_scalbn.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\libm\s_sin.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\libm\s_tan.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\Frank Work All\CPP\f0\libs\SDL3\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{DDEB7204-6215-3B87-9EC8-9E7DAEA656E3}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
