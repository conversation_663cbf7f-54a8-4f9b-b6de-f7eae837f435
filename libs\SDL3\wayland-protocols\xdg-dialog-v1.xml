<?xml version="1.0" encoding="UTF-8"?>
<protocol name="xdg_dialog_v1">
  <copyright>
    Copyright © 2023 Carlos <PERSON>ho

    Permission is hereby granted, free of charge, to any person obtaining a
    copy of this software and associated documentation files (the "Software"),
    to deal in the Software without restriction, including without limitation
    the rights to use, copy, modify, merge, publish, distribute, sublicense,
    and/or sell copies of the Software, and to permit persons to whom the
    Software is furnished to do so, subject to the following conditions:

    The above copyright notice and this permission notice (including the next
    paragraph) shall be included in all copies or substantial portions of the
    Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
    THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
    FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
    DEALINGS IN THE SOFTWARE.
  </copyright>

  <interface name="xdg_wm_dialog_v1" version="1">
    <description summary="create dialogs related to other toplevels">
      The xdg_wm_dialog_v1 interface is exposed as a global object allowing
      to register surfaces with a xdg_toplevel role as "dialogs" relative to
      another toplevel.

      The compositor may let this relation influence how the surface is
      placed, displayed or interacted with.

      Warning! The protocol described in this file is currently in the testing
      phase. Backward compatible changes may be added together with the
      corresponding interface version bump. Backward incompatible changes can
      only be done by creating a new major version of the extension.
    </description>

    <enum name="error">
      <entry name="already_used" value="0"
             summary="the xdg_toplevel object has already been used to create a xdg_dialog_v1"/>
    </enum>

    <request name="destroy" type="destructor">
      <description summary="destroy the dialog manager object">
        Destroys the xdg_wm_dialog_v1 object. This does not affect
        the xdg_dialog_v1 objects generated through it.
      </description>
    </request>

    <request name="get_xdg_dialog">
      <description summary="create a dialog object">
        Creates a xdg_dialog_v1 object for the given toplevel. See the interface
        description for more details.

	Compositors must raise an already_used error if clients attempt to
	create multiple xdg_dialog_v1 objects for the same xdg_toplevel.
      </description>
      <arg name="id" type="new_id" interface="xdg_dialog_v1"/>
      <arg name="toplevel" type="object" interface="xdg_toplevel"/>
    </request>
  </interface>

  <interface name="xdg_dialog_v1" version="1">
    <description summary="dialog object">
      A xdg_dialog_v1 object is an ancillary object tied to a xdg_toplevel. Its
      purpose is hinting the compositor that the toplevel is a "dialog" (e.g. a
      temporary window) relative to another toplevel (see
      xdg_toplevel.set_parent). If the xdg_toplevel is destroyed, the xdg_dialog_v1
      becomes inert.

      Through this object, the client may provide additional hints about
      the purpose of the secondary toplevel. This interface has no effect
      on toplevels that are not attached to a parent toplevel.
    </description>

    <request name="destroy" type="destructor">
      <description summary="destroy the dialog object">
        Destroys the xdg_dialog_v1 object. If this object is destroyed
        before the related xdg_toplevel, the compositor should unapply its
        effects.
      </description>
    </request>

    <request name="set_modal">
      <description summary="mark dialog as modal">
        Hints that the dialog has "modal" behavior. Modal dialogs typically
        require to be fully addressed by the user (i.e. closed) before resuming
        interaction with the parent toplevel, and may require a distinct
        presentation.

        Clients must implement the logic to filter events in the parent
        toplevel on their own.

        Compositors may choose any policy in event delivery to the parent
        toplevel, from delivering all events unfiltered to using them for
        internal consumption.
      </description>
    </request>

    <request name="unset_modal">
      <description summary="mark dialog as not modal">
        Drops the hint that this dialog has "modal" behavior. See
        xdg_dialog_v1.set_modal for more details.
      </description>
    </request>
  </interface>
</protocol>
