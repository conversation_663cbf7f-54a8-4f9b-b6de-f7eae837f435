{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "libs/SDL3/Release/SDL_uclibc.lib"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "add_compile_options", "target_compile_options", "SDL_AddCommonCompilerFlags", "target_compile_definitions", "target_include_directories"], "files": ["libs/SDL3/CMakeLists.txt", "libs/SDL3/cmake/sdlcompilers.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 1226, "parent": 0}, {"command": 1, "file": 0, "line": 19, "parent": 0}, {"command": 3, "file": 0, "line": 1231, "parent": 0}, {"command": 2, "file": 1, "line": 37, "parent": 3}, {"command": 4, "file": 0, "line": 1227, "parent": 0}, {"command": 5, "file": 0, "line": 1228, "parent": 0}, {"command": 5, "file": 0, "line": 1229, "parent": 0}, {"command": 5, "file": 0, "line": 1230, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /O2 /Ob2 /DNDEBUG -MD"}, {"backtrace": 2, "fragment": "/utf-8"}, {"backtrace": 4, "fragment": "/W3"}], "defines": [{"backtrace": 5, "define": "USING_GENERATED_CONFIG_H"}], "includes": [{"backtrace": 6, "path": "D:/<PERSON> All/CPP/f0/build/libs/SDL3/include-config-release/build_config"}, {"backtrace": 7, "path": "D:/<PERSON>/CPP/f0/libs/SDL3/src"}, {"backtrace": 8, "path": "D:/<PERSON> All/CPP/f0/libs/SDL3/include"}], "language": "C", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "SDL_uclibc::@1536e03098708b12d592", "name": "SDL_uclibc", "nameOnDisk": "SDL_uclibc.lib", "paths": {"build": "libs/SDL3", "source": "libs/SDL3"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "libs/SDL3/src/libm/e_atan2.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libs/SDL3/src/libm/e_exp.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libs/SDL3/src/libm/e_fmod.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libs/SDL3/src/libm/e_log.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libs/SDL3/src/libm/e_log10.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libs/SDL3/src/libm/e_pow.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libs/SDL3/src/libm/e_rem_pio2.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libs/SDL3/src/libm/e_sqrt.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libs/SDL3/src/libm/k_cos.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libs/SDL3/src/libm/k_rem_pio2.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libs/SDL3/src/libm/k_sin.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libs/SDL3/src/libm/k_tan.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libs/SDL3/src/libm/s_atan.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libs/SDL3/src/libm/s_copysign.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libs/SDL3/src/libm/s_cos.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libs/SDL3/src/libm/s_fabs.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libs/SDL3/src/libm/s_floor.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libs/SDL3/src/libm/s_isinf.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libs/SDL3/src/libm/s_isinff.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libs/SDL3/src/libm/s_isnan.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libs/SDL3/src/libm/s_isnanf.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libs/SDL3/src/libm/s_modf.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libs/SDL3/src/libm/s_scalbn.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libs/SDL3/src/libm/s_sin.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libs/SDL3/src/libm/s_tan.c", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}