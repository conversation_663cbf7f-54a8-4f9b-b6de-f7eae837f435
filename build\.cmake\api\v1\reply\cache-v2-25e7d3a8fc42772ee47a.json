{"entries": [{"name": "CMAKE_AR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lib.exe"}, {"name": "CMAKE_BUILD_TYPE", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "STRING", "value": "Debug"}, {"name": "CMAKE_CACHEFILE_DIR", "properties": [{"name": "HELPSTRING", "value": "This is the directory where this CMakeCache.txt was created"}], "type": "INTERNAL", "value": "d:/<PERSON>/CPP/f0/build"}, {"name": "CMAKE_CACHE_MAJOR_VERSION", "properties": [{"name": "HELPSTRING", "value": "Major version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "4"}, {"name": "CMAKE_CACHE_MINOR_VERSION", "properties": [{"name": "HELPSTRING", "value": "Minor version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_CACHE_PATCH_VERSION", "properties": [{"name": "HELPSTRING", "value": "Patch version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "0"}, {"name": "CMAKE_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to CMake executable."}], "type": "INTERNAL", "value": "C:/Program Files/CMake/bin/cmake.exe"}, {"name": "CMAKE_CONFIGURATION_TYPES", "properties": [{"name": "HELPSTRING", "value": "Semicolon separated list of supported configuration types, only supports Debug, Release, MinSizeRel, and RelWithDebInfo, anything else will be ignored."}], "type": "STRING", "value": "Debug;Release;MinSizeRel;RelWithDebInfo"}, {"name": "CMAKE_CPACK_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to cpack program executable."}], "type": "INTERNAL", "value": "C:/Program Files/CMake/bin/cpack.exe"}, {"name": "CMAKE_CTEST_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to ctest program executable."}], "type": "INTERNAL", "value": "C:/Program Files/CMake/bin/ctest.exe"}, {"name": "CMAKE_CXX_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during all build types."}], "type": "STRING", "value": "/DWIN32 /D_WINDOWS /GR /EHsc"}, {"name": "CMAKE_CXX_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during DEBUG builds."}], "type": "STRING", "value": "/Zi /Ob0 /Od /RTC1"}, {"name": "CMAKE_CXX_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during MINSIZEREL builds."}], "type": "STRING", "value": "/O1 /Ob1 /DNDEBUG"}, {"name": "CMAKE_CXX_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during RELEASE builds."}], "type": "STRING", "value": "/O2 /Ob2 /DNDEBUG"}, {"name": "CMAKE_CXX_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during RELWITHDEBINFO builds."}], "type": "STRING", "value": "/Zi /O2 /Ob1 /DNDEBUG"}, {"name": "CMAKE_CXX_STANDARD_LIBRARIES", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Libraries linked by default with all C++ applications."}], "type": "STRING", "value": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib"}, {"name": "CMAKE_C_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during all build types."}], "type": "STRING", "value": "/DWIN32 /D_WINDOWS"}, {"name": "CMAKE_C_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during DEBUG builds."}], "type": "STRING", "value": "/Zi /Ob0 /Od /RTC1"}, {"name": "CMAKE_C_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during MINSIZEREL builds."}], "type": "STRING", "value": "/O1 /Ob1 /DNDEBUG"}, {"name": "CMAKE_C_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during RELEASE builds."}], "type": "STRING", "value": "/O2 /Ob2 /DNDEBUG"}, {"name": "CMAKE_C_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during RELWITHDEBINFO builds."}], "type": "STRING", "value": "/Zi /O2 /Ob1 /DNDEBUG"}, {"name": "CMAKE_C_STANDARD_LIBRARIES", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Libraries linked by default with all C applications."}], "type": "STRING", "value": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib"}, {"name": "CMAKE_EXECUTABLE_FORMAT", "properties": [{"name": "HELPSTRING", "value": "Executable file format"}], "type": "INTERNAL", "value": "Unknown"}, {"name": "CMAKE_EXE_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during all build types."}], "type": "STRING", "value": "/machine:x64"}, {"name": "CMAKE_EXE_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during DEBUG builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_EXE_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during MINSIZEREL builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_EXE_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during RELEASE builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during RELWITHDEBINFO builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_EXPORT_COMPILE_COMMANDS", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "BOOL", "value": "TRUE"}, {"name": "CMAKE_EXTRA_GENERATOR", "properties": [{"name": "HELPSTRING", "value": "Name of external makefile project generator."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_FIND_PACKAGE_REDIRECTS_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake."}], "type": "STATIC", "value": "D:/Frank Work All/CPP/f0/build/CMakeFiles/pkgRedirects"}, {"name": "CMAKE_GENERATOR", "properties": [{"name": "HELPSTRING", "value": "Name of generator."}], "type": "INTERNAL", "value": "Visual Studio 17 2022"}, {"name": "CMAKE_GENERATOR_INSTANCE", "properties": [{"name": "HELPSTRING", "value": "Generator instance identifier."}], "type": "INTERNAL", "value": "C:/Program Files/Microsoft Visual Studio/2022/Community"}, {"name": "CMAKE_GENERATOR_PLATFORM", "properties": [{"name": "HELPSTRING", "value": "Name of generator platform."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_GENERATOR_TOOLSET", "properties": [{"name": "HELPSTRING", "value": "Name of generator toolset."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_HOME_DIRECTORY", "properties": [{"name": "HELPSTRING", "value": "Source directory with the top level CMakeLists.txt file for this project"}], "type": "INTERNAL", "value": "D:/<PERSON>/CPP/f0"}, {"name": "CMAKE_INSTALL_BINDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "User executables (bin)"}], "type": "PATH", "value": "bin"}, {"name": "CMAKE_INSTALL_DATADIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Read-only architecture-independent data (DATAROOTDIR)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_DATAROOTDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Read-only architecture-independent data root (share)"}], "type": "PATH", "value": "share"}, {"name": "CMAKE_INSTALL_DOCDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Documentation root (DATAROOTDIR/doc/PROJECT_NAME)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_INCLUDEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "C header files (include)"}], "type": "PATH", "value": "include"}, {"name": "CMAKE_INSTALL_INFODIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Info documentation (DATAROOTDIR/info)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_LIBDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Object code libraries (lib)"}], "type": "PATH", "value": "lib"}, {"name": "CMAKE_INSTALL_LIBEXECDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Program executables (libexec)"}], "type": "PATH", "value": "libexec"}, {"name": "CMAKE_INSTALL_LOCALEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Locale-dependent data (DATAROOTDIR/locale)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_LOCALSTATEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Modifiable single-machine data (var)"}], "type": "PATH", "value": "var"}, {"name": "CMAKE_INSTALL_MANDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Man documentation (DATAROOTDIR/man)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_OLDINCLUDEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "C header files for non-gcc (/usr/include)"}], "type": "PATH", "value": "/usr/include"}, {"name": "CMAKE_INSTALL_PREFIX", "properties": [{"name": "HELPSTRING", "value": "Install path prefix, prepended onto install directories."}], "type": "PATH", "value": "C:/Program Files (x86)/MySDL3Game"}, {"name": "CMAKE_INSTALL_RUNSTATEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Run-time variable data (LOCALSTATEDIR/run)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_SBINDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "System admin executables (sbin)"}], "type": "PATH", "value": "sbin"}, {"name": "CMAKE_INSTALL_SHAREDSTATEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Modifiable architecture-independent data (com)"}], "type": "PATH", "value": "com"}, {"name": "CMAKE_INSTALL_SYSCONFDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Read-only single-machine data (etc)"}], "type": "PATH", "value": "etc"}, {"name": "CMAKE_LINKER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/link.exe"}, {"name": "CMAKE_LIST_FILE_NAME", "properties": [{"name": "HELPSTRING", "value": "Name of CMakeLists files to read"}], "type": "INTERNAL", "value": "CMakeLists.txt"}, {"name": "CMAKE_MODULE_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during all build types."}], "type": "STRING", "value": "/machine:x64"}, {"name": "CMAKE_MODULE_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during DEBUG builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during MINSIZEREL builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_MODULE_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during RELEASE builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during RELWITHDEBINFO builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_MT", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "CMAKE_MT-NOTFOUND"}, {"name": "CMAKE_NUMBER_OF_MAKEFILES", "properties": [{"name": "HELPSTRING", "value": "number of local generators"}], "type": "INTERNAL", "value": "2"}, {"name": "CMAKE_PLATFORM_INFO_INITIALIZED", "properties": [{"name": "HELPSTRING", "value": "Platform information initialized"}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_PROJECT_COMPAT_VERSION", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_DESCRIPTION", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_HOMEPAGE_URL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_NAME", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "MySDL3Game"}, {"name": "CMAKE_PROJECT_VERSION", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_VERSION_MAJOR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_VERSION_MINOR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_VERSION_PATCH", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_VERSION_TWEAK", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_RANLIB", "properties": [{"name": "HELPSTRING", "value": "noop for ranlib"}], "type": "INTERNAL", "value": ":"}, {"name": "CMAKE_RC_COMPILER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "RC compiler"}], "type": "FILEPATH", "value": "rc"}, {"name": "CMAKE_RC_COMPILER_WORKS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_RC_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during all build types."}], "type": "STRING", "value": "-DWIN32"}, {"name": "CMAKE_RC_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during DEBUG builds."}], "type": "STRING", "value": "-D_DEBUG"}, {"name": "CMAKE_RC_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_RC_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_RC_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_ROOT", "properties": [{"name": "HELPSTRING", "value": "Path to CMake installation."}], "type": "INTERNAL", "value": "C:/Program Files/CMake/share/cmake-4.1"}, {"name": "CMAKE_SHARED_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during all build types."}], "type": "STRING", "value": "/machine:x64"}, {"name": "CMAKE_SHARED_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during DEBUG builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during MINSIZEREL builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_SHARED_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during RELEASE builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during RELWITHDEBINFO builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_SKIP_INSTALL_RPATH", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If set, runtime paths are not added when installing shared libraries, but are added when building."}], "type": "BOOL", "value": "NO"}, {"name": "CMAKE_SKIP_RPATH", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If set, runtime paths are not added when using shared libraries."}], "type": "BOOL", "value": "NO"}, {"name": "CMAKE_STATIC_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the archiver during the creation of static libraries during all build types."}], "type": "STRING", "value": "/machine:x64"}, {"name": "CMAKE_STATIC_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the archiver during the creation of static libraries during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the archiver during the creation of static libraries during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the archiver during the creation of static libraries during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the archiver during the creation of static libraries during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_VERBOSE_MAKEFILE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If this value is on, makefiles will be generated without the .SILENT directive, and all commands will be echoed to the console during the make.  This is useful for debugging only. With Visual Studio IDE projects all commands are done without /nologo."}], "type": "BOOL", "value": "FALSE"}, {"name": "COMPILER_SUPPORTS_AVX", "properties": [{"name": "HELPSTRING", "value": "Test COMPILER_SUPPORTS_AVX"}], "type": "INTERNAL", "value": "1"}, {"name": "COMPILER_SUPPORTS_AVX2", "properties": [{"name": "HELPSTRING", "value": "Test COMPILER_SUPPORTS_AVX2"}], "type": "INTERNAL", "value": ""}, {"name": "COMPILER_SUPPORTS_AVX512F", "properties": [{"name": "HELPSTRING", "value": "Test COMPILER_SUPPORTS_AVX512F"}], "type": "INTERNAL", "value": "1"}, {"name": "COMPILER_SUPPORTS_FDIAGNOSTICS_COLOR_ALWAYS", "properties": [{"name": "HELPSTRING", "value": "Test COMPILER_SUPPORTS_FDIAGNOSTICS_COLOR_ALWAYS"}], "type": "INTERNAL", "value": ""}, {"name": "COMPILER_SUPPORTS_MMX", "properties": [{"name": "HELPSTRING", "value": "Test COMPILER_SUPPORTS_MMX"}], "type": "INTERNAL", "value": ""}, {"name": "COMPILER_SUPPORTS_SSE", "properties": [{"name": "HELPSTRING", "value": "Test COMPILER_SUPPORTS_SSE"}], "type": "INTERNAL", "value": "1"}, {"name": "COMPILER_SUPPORTS_SSE2", "properties": [{"name": "HELPSTRING", "value": "Test COMPILER_SUPPORTS_SSE2"}], "type": "INTERNAL", "value": "1"}, {"name": "COMPILER_SUPPORTS_SSE3", "properties": [{"name": "HELPSTRING", "value": "Test COMPILER_SUPPORTS_SSE3"}], "type": "INTERNAL", "value": "1"}, {"name": "COMPILER_SUPPORTS_SSE4_1", "properties": [{"name": "HELPSTRING", "value": "Test COMPILER_SUPPORTS_SSE4_1"}], "type": "INTERNAL", "value": "1"}, {"name": "COMPILER_SUPPORTS_SSE4_2", "properties": [{"name": "HELPSTRING", "value": "Test COMPILER_SUPPORTS_SSE4_2"}], "type": "INTERNAL", "value": "1"}, {"name": "COMPILER_SUPPORTS_W3", "properties": [{"name": "HELPSTRING", "value": "Test /W3"}], "type": "INTERNAL", "value": "1"}, {"name": "GIT_EXECUTABLE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Git command line client"}], "type": "FILEPATH", "value": "C:/Program Files/Git/cmd/git.exe"}, {"name": "HAVE_ALLOCA_H", "properties": [{"name": "HELPSTRING", "value": "Have include alloca.h"}], "type": "INTERNAL", "value": ""}, {"name": "HAVE_AUDIOCLIENT_H", "properties": [{"name": "HELPSTRING", "value": "Have include audioclient.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_D3D11_H", "properties": [{"name": "HELPSTRING", "value": "Have include d3d11_1.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_D3D9_H", "properties": [{"name": "HELPSTRING", "value": "Have include d3d9.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_DDRAW_H", "properties": [{"name": "HELPSTRING", "value": "Have include ddraw.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_DINPUT_H", "properties": [{"name": "HELPSTRING", "value": "Have include dinput.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_DSOUND_H", "properties": [{"name": "HELPSTRING", "value": "Have include dsound.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_DXGI1_6_H", "properties": [{"name": "HELPSTRING", "value": "Have include dxgi1_6.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_DXGI_H", "properties": [{"name": "HELPSTRING", "value": "Have include dxgi.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_GAMEINPUT_H", "properties": [{"name": "HELPSTRING", "value": "Test HAVE_GAMEINPUT_H"}], "type": "INTERNAL", "value": ""}, {"name": "HAVE_LIBM", "properties": [{"name": "HELPSTRING", "value": "Have library m"}], "type": "INTERNAL", "value": ""}, {"name": "HAVE_MALLOC", "properties": [{"name": "HELPSTRING", "value": "Have include malloc.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_MALLOC_H", "properties": [{"name": "HELPSTRING", "value": "Have include malloc.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_MFAPI_H", "properties": [{"name": "HELPSTRING", "value": "Test HAVE_MFAPI_H"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_MMDEVICEAPI_H", "properties": [{"name": "HELPSTRING", "value": "Have include mmdeviceapi.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_ROAPI_H", "properties": [{"name": "HELPSTRING", "value": "Have include roapi.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_SENSORSAPI_H", "properties": [{"name": "HELPSTRING", "value": "Have include sensorsapi.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_SHELLSCALINGAPI_H", "properties": [{"name": "HELPSTRING", "value": "Have include shellscalingapi.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_TPCSHRD_H", "properties": [{"name": "HELPSTRING", "value": "Have include tpcshrd.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_WIN32_CC", "properties": [{"name": "HELPSTRING", "value": "Test HAVE_WIN32_CC"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_WINDOWS_GAMING_INPUT_H", "properties": [{"name": "HELPSTRING", "value": "Test HAVE_WINDOWS_GAMING_INPUT_H"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_XINPUT_H", "properties": [{"name": "HELPSTRING", "value": "Test HAVE_XINPUT_H"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_ABS", "properties": [{"name": "HELPSTRING", "value": "Have symbol abs"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_ACOS", "properties": [{"name": "HELPSTRING", "value": "Have symbol acos"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_ACOSF", "properties": [{"name": "HELPSTRING", "value": "Have symbol acosf"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_ASIN", "properties": [{"name": "HELPSTRING", "value": "Have symbol asin"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_ASINF", "properties": [{"name": "HELPSTRING", "value": "Have symbol asinf"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_ATAN", "properties": [{"name": "HELPSTRING", "value": "Have symbol atan"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_ATAN2", "properties": [{"name": "HELPSTRING", "value": "Have symbol atan2"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_ATAN2F", "properties": [{"name": "HELPSTRING", "value": "Have symbol atan2f"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_ATANF", "properties": [{"name": "HELPSTRING", "value": "Have symbol atanf"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_ATOF", "properties": [{"name": "HELPSTRING", "value": "Have symbol atof"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_ATOI", "properties": [{"name": "HELPSTRING", "value": "Have symbol atoi"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_BCOPY", "properties": [{"name": "HELPSTRING", "value": "Have symbol bcopy"}], "type": "INTERNAL", "value": ""}, {"name": "LIBC_HAS_CALLOC", "properties": [{"name": "HELPSTRING", "value": "Have symbol calloc"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_CEIL", "properties": [{"name": "HELPSTRING", "value": "Have symbol ceil"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_CEILF", "properties": [{"name": "HELPSTRING", "value": "Have symbol ceilf"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_COPYSIGN", "properties": [{"name": "HELPSTRING", "value": "Have symbol copysign"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_COPYSIGNF", "properties": [{"name": "HELPSTRING", "value": "Have symbol copysignf"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_COS", "properties": [{"name": "HELPSTRING", "value": "Have symbol cos"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_COSF", "properties": [{"name": "HELPSTRING", "value": "Have symbol cosf"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_EXP", "properties": [{"name": "HELPSTRING", "value": "Have symbol exp"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_EXPF", "properties": [{"name": "HELPSTRING", "value": "Have symbol expf"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_FABS", "properties": [{"name": "HELPSTRING", "value": "Have symbol fabs"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_FABSF", "properties": [{"name": "HELPSTRING", "value": "Have symbol fabsf"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_FLOAT_H", "properties": [{"name": "HELPSTRING", "value": "Have include float.h"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_FLOOR", "properties": [{"name": "HELPSTRING", "value": "Have symbol floor"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_FLOORF", "properties": [{"name": "HELPSTRING", "value": "Have symbol floorf"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_FMOD", "properties": [{"name": "HELPSTRING", "value": "Have symbol fmod"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_FMODF", "properties": [{"name": "HELPSTRING", "value": "Have symbol fmodf"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_FOPEN64", "properties": [{"name": "HELPSTRING", "value": "Have symbol fopen64"}], "type": "INTERNAL", "value": ""}, {"name": "LIBC_HAS_FREE", "properties": [{"name": "HELPSTRING", "value": "Have symbol free"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_FSEEKO", "properties": [{"name": "HELPSTRING", "value": "Have symbol fseeko"}], "type": "INTERNAL", "value": ""}, {"name": "LIBC_HAS_FSEEKO64", "properties": [{"name": "HELPSTRING", "value": "Have symbol fseeko64"}], "type": "INTERNAL", "value": ""}, {"name": "LIBC_HAS_GETENV", "properties": [{"name": "HELPSTRING", "value": "Have symbol getenv"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_ICONV_H", "properties": [{"name": "HELPSTRING", "value": "Have include iconv.h"}], "type": "INTERNAL", "value": ""}, {"name": "LIBC_HAS_INDEX", "properties": [{"name": "HELPSTRING", "value": "Have symbol index"}], "type": "INTERNAL", "value": ""}, {"name": "LIBC_HAS_INTTYPES_H", "properties": [{"name": "HELPSTRING", "value": "Have include inttypes.h"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_ISINF", "properties": [{"name": "HELPSTRING", "value": "Have include isinf(double)"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_ISINFF", "properties": [{"name": "HELPSTRING", "value": "Have include is<PERSON><PERSON>(float)"}], "type": "INTERNAL", "value": ""}, {"name": "LIBC_HAS_ISNAN", "properties": [{"name": "HELPSTRING", "value": "Have include isnan(double)"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_ISNANF", "properties": [{"name": "HELPSTRING", "value": "Have include isn<PERSON><PERSON>(float)"}], "type": "INTERNAL", "value": ""}, {"name": "LIBC_HAS_ITOA", "properties": [{"name": "HELPSTRING", "value": "Have symbol itoa"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_LIMITS_H", "properties": [{"name": "HELPSTRING", "value": "Have include limits.h"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_LOG", "properties": [{"name": "HELPSTRING", "value": "Have symbol log"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_LOG10", "properties": [{"name": "HELPSTRING", "value": "Have symbol log10"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_LOG10F", "properties": [{"name": "HELPSTRING", "value": "Have symbol log10f"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_LOGF", "properties": [{"name": "HELPSTRING", "value": "Have symbol logf"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_LROUND", "properties": [{"name": "HELPSTRING", "value": "Have symbol lround"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_LROUNDF", "properties": [{"name": "HELPSTRING", "value": "Have symbol lroundf"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_MALLOC", "properties": [{"name": "HELPSTRING", "value": "Have symbol malloc"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_MALLOC_H", "properties": [{"name": "HELPSTRING", "value": "Have include malloc.h"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_MATH_H", "properties": [{"name": "HELPSTRING", "value": "Have include math.h"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_MEMCMP", "properties": [{"name": "HELPSTRING", "value": "Have symbol memcmp"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_MEMCPY", "properties": [{"name": "HELPSTRING", "value": "Have symbol memcpy"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_MEMMOVE", "properties": [{"name": "HELPSTRING", "value": "Have symbol memmove"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_MEMORY_H", "properties": [{"name": "HELPSTRING", "value": "Have include memory.h"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_MEMSET", "properties": [{"name": "HELPSTRING", "value": "Have symbol memset"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_MODF", "properties": [{"name": "HELPSTRING", "value": "Have symbol modf"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_MODFF", "properties": [{"name": "HELPSTRING", "value": "Have symbol modff"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_POW", "properties": [{"name": "HELPSTRING", "value": "Have symbol pow"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_POWF", "properties": [{"name": "HELPSTRING", "value": "Have symbol powf"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_PUTENV", "properties": [{"name": "HELPSTRING", "value": "Have symbol putenv"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_REALLOC", "properties": [{"name": "HELPSTRING", "value": "Have symbol realloc"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_RINDEX", "properties": [{"name": "HELPSTRING", "value": "Have symbol rindex"}], "type": "INTERNAL", "value": ""}, {"name": "LIBC_HAS_ROUND", "properties": [{"name": "HELPSTRING", "value": "Have symbol round"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_ROUNDF", "properties": [{"name": "HELPSTRING", "value": "Have symbol roundf"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_SCALBN", "properties": [{"name": "HELPSTRING", "value": "Have symbol scalbn"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_SCALBNF", "properties": [{"name": "HELPSTRING", "value": "Have symbol scalbnf"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_SETENV", "properties": [{"name": "HELPSTRING", "value": "Have symbol setenv"}], "type": "INTERNAL", "value": ""}, {"name": "LIBC_HAS_SIGNAL_H", "properties": [{"name": "HELPSTRING", "value": "Have include signal.h"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_SIN", "properties": [{"name": "HELPSTRING", "value": "Have symbol sin"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_SINF", "properties": [{"name": "HELPSTRING", "value": "Have symbol sinf"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_SQR", "properties": [{"name": "HELPSTRING", "value": "Have symbol sqr"}], "type": "INTERNAL", "value": ""}, {"name": "LIBC_HAS_SQRT", "properties": [{"name": "HELPSTRING", "value": "Have symbol sqrt"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_SQRTF", "properties": [{"name": "HELPSTRING", "value": "Have symbol sqrtf"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_SSCANF", "properties": [{"name": "HELPSTRING", "value": "Have symbol sscanf"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_STDARG_H", "properties": [{"name": "HELPSTRING", "value": "Have include stdarg.h"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_STDBOOL_H", "properties": [{"name": "HELPSTRING", "value": "Have include stdbool.h"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_STDDEF_H", "properties": [{"name": "HELPSTRING", "value": "Have include stddef.h"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_STDINT_H", "properties": [{"name": "HELPSTRING", "value": "Have include stdint.h"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_STDIO_H", "properties": [{"name": "HELPSTRING", "value": "Have include stdio.h"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_STDLIB_H", "properties": [{"name": "HELPSTRING", "value": "Have include stdlib.h"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_STRCHR", "properties": [{"name": "HELPSTRING", "value": "Have symbol strchr"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_STRCMP", "properties": [{"name": "HELPSTRING", "value": "Have symbol strcmp"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_STRINGS_H", "properties": [{"name": "HELPSTRING", "value": "Have include strings.h"}], "type": "INTERNAL", "value": ""}, {"name": "LIBC_HAS_STRING_H", "properties": [{"name": "HELPSTRING", "value": "Have include string.h"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_STRLCAT", "properties": [{"name": "HELPSTRING", "value": "Have symbol strlcat"}], "type": "INTERNAL", "value": ""}, {"name": "LIBC_HAS_STRLCPY", "properties": [{"name": "HELPSTRING", "value": "Have symbol strlcpy"}], "type": "INTERNAL", "value": ""}, {"name": "LIBC_HAS_STRLEN", "properties": [{"name": "HELPSTRING", "value": "Have symbol strlen"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_STRNCMP", "properties": [{"name": "HELPSTRING", "value": "Have symbol strncmp"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_STRNLEN", "properties": [{"name": "HELPSTRING", "value": "Have symbol strnlen"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_STRNSTR", "properties": [{"name": "HELPSTRING", "value": "Have symbol strnstr"}], "type": "INTERNAL", "value": ""}, {"name": "LIBC_HAS_STRPBRK", "properties": [{"name": "HELPSTRING", "value": "Have symbol strpbrk"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_STRRCHR", "properties": [{"name": "HELPSTRING", "value": "Have symbol strrchr"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_STRSTR", "properties": [{"name": "HELPSTRING", "value": "Have symbol strstr"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_STRTOD", "properties": [{"name": "HELPSTRING", "value": "Have symbol strtod"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_STRTOK_R", "properties": [{"name": "HELPSTRING", "value": "Have symbol strtok_r"}], "type": "INTERNAL", "value": ""}, {"name": "LIBC_HAS_STRTOL", "properties": [{"name": "HELPSTRING", "value": "Have symbol strtol"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_STRTOLL", "properties": [{"name": "HELPSTRING", "value": "Have symbol strtoll"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_STRTOUL", "properties": [{"name": "HELPSTRING", "value": "Have symbol strtoul"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_STRTOULL", "properties": [{"name": "HELPSTRING", "value": "Have symbol strtoull"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_SYS_TYPES_H", "properties": [{"name": "HELPSTRING", "value": "Have include sys/types.h"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_TAN", "properties": [{"name": "HELPSTRING", "value": "Have symbol tan"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_TANF", "properties": [{"name": "HELPSTRING", "value": "Have symbol tanf"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_TIME_H", "properties": [{"name": "HELPSTRING", "value": "Have include time.h"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_TRUNC", "properties": [{"name": "HELPSTRING", "value": "Have symbol trunc"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_TRUNCF", "properties": [{"name": "HELPSTRING", "value": "Have symbol truncf"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_UNSETENV", "properties": [{"name": "HELPSTRING", "value": "Have symbol unsetenv"}], "type": "INTERNAL", "value": ""}, {"name": "LIBC_HAS_VSNPRINTF", "properties": [{"name": "HELPSTRING", "value": "Have symbol vsnprintf"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_VSSCANF", "properties": [{"name": "HELPSTRING", "value": "Have symbol vsscanf"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_WCHAR_H", "properties": [{"name": "HELPSTRING", "value": "Have include wchar.h"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_WCSCMP", "properties": [{"name": "HELPSTRING", "value": "Have symbol wcscmp"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_WCSDUP", "properties": [{"name": "HELPSTRING", "value": "Have symbol wcsdup"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_WCSLCAT", "properties": [{"name": "HELPSTRING", "value": "Have symbol wcslcat"}], "type": "INTERNAL", "value": ""}, {"name": "LIBC_HAS_WCSLCPY", "properties": [{"name": "HELPSTRING", "value": "Have symbol wcslcpy"}], "type": "INTERNAL", "value": ""}, {"name": "LIBC_HAS_WCSLEN", "properties": [{"name": "HELPSTRING", "value": "Have symbol wcslen"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_WCSNCMP", "properties": [{"name": "HELPSTRING", "value": "Have symbol wcsncmp"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_WCSNLEN", "properties": [{"name": "HELPSTRING", "value": "Have symbol wcsnlen"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_WCSSTR", "properties": [{"name": "HELPSTRING", "value": "Have symbol wcsstr"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS_WCSTOL", "properties": [{"name": "HELPSTRING", "value": "Have symbol wcstol"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS__COPYSIGN", "properties": [{"name": "HELPSTRING", "value": "Have symbol _copysign"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS__EXIT", "properties": [{"name": "HELPSTRING", "value": "Have symbol _Exit"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS__FSEEKI64", "properties": [{"name": "HELPSTRING", "value": "Have symbol _fseeki64"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS__I64TOA", "properties": [{"name": "HELPSTRING", "value": "Have symbol _i64toa"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS__LTOA", "properties": [{"name": "HELPSTRING", "value": "Have symbol _ltoa"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS__STRREV", "properties": [{"name": "HELPSTRING", "value": "Have symbol _strrev"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS__UI64TOA", "properties": [{"name": "HELPSTRING", "value": "Have symbol _ui64toa"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS__UITOA", "properties": [{"name": "HELPSTRING", "value": "Have symbol _uitoa"}], "type": "INTERNAL", "value": ""}, {"name": "LIBC_HAS__ULTOA", "properties": [{"name": "HELPSTRING", "value": "Have symbol _ultoa"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_HAS__WCSDUP", "properties": [{"name": "HELPSTRING", "value": "Have symbol _wcsdup"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_ISINF_HANDLES_FLOAT", "properties": [{"name": "HELPSTRING", "value": "Have include isinf(float)"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_ISNAN_HANDLES_FLOAT", "properties": [{"name": "HELPSTRING", "value": "Have include isnan(float)"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBC_IS_GLIBC", "properties": [{"name": "HELPSTRING", "value": "Have symbol __GLIBC__"}], "type": "INTERNAL", "value": ""}, {"name": "LibUSB_COMPILE_OPTIONS", "properties": [{"name": "HELPSTRING", "value": "Extra compile options of LibUSB"}], "type": "STRING", "value": ""}, {"name": "LibUSB_INCLUDE_PATH", "properties": [{"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "LibUSB_INCLUDE_PATH-NOTFOUND"}, {"name": "LibUSB_LIBRARY", "properties": [{"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "LibUSB_LIBRARY-NOTFOUND"}, {"name": "LibUSB_LINK_FLAGS", "properties": [{"name": "HELPSTRING", "value": "Extra link flags of LibUSB"}], "type": "STRING", "value": ""}, {"name": "LibUSB_LINK_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": "Extra link libraries of LibUSB"}], "type": "STRING", "value": ""}, {"name": "MySDL3Game_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/<PERSON>/CPP/f0/build"}, {"name": "MySDL3Game_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "ON"}, {"name": "MySDL3Game_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/<PERSON>/CPP/f0"}, {"name": "PKG_CONFIG_ARGN", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Arguments to supply to pkg-config"}], "type": "STRING", "value": ""}, {"name": "PKG_CONFIG_EXECUTABLE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "pkg-config executable"}], "type": "FILEPATH", "value": "PKG_CONFIG_EXECUTABLE-NOTFOUND"}, {"name": "SDL3_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/<PERSON> All/CPP/f0/build/libs/SDL3"}, {"name": "SDL3_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "SDL3_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "D:/<PERSON>/CPP/f0/libs/SDL3"}, {"name": "SDL_ALSA", "properties": [{"name": "HELPSTRING", "value": "Support the ALSA audio API"}], "type": "BOOL", "value": "OFF"}, {"name": "SDL_ASAN", "properties": [{"name": "HELPSTRING", "value": "Use AddressSanitizer to detect memory errors"}], "type": "BOOL", "value": "OFF"}, {"name": "SDL_ASSEMBLY", "properties": [{"name": "HELPSTRING", "value": "Enable assembly routines"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_ASSERTIONS", "properties": [{"name": "HELPSTRING", "value": "Enable internal sanity checks (auto/disabled/release/enabled/paranoid)"}], "type": "STRING", "value": "auto"}, {"name": "SDL_AUDIO", "properties": [{"name": "HELPSTRING", "value": "Enable the Audio subsystem"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_AVX", "properties": [{"name": "HELPSTRING", "value": "Use AVX assembly routines"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_AVX2", "properties": [{"name": "HELPSTRING", "value": "Use AVX2 assembly routines"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_AVX512F", "properties": [{"name": "HELPSTRING", "value": "Use AVX512F assembly routines"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_BACKGROUNDING_SIGNAL", "properties": [{"name": "HELPSTRING", "value": "number to use for magic backgrounding signal or 'OFF'"}], "type": "STRING", "value": "OFF"}, {"name": "SDL_CAMERA", "properties": [{"name": "HELPSTRING", "value": "Enable the Camera subsystem"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_CCACHE", "properties": [{"name": "HELPSTRING", "value": "Use Ccache to speed up build"}], "type": "BOOL", "value": "OFF"}, {"name": "SDL_CHECK_REQUIRED_INCLUDES", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Extra includes (for CMAKE_REQUIRED_INCLUDES)"}], "type": "STRING", "value": ""}, {"name": "SDL_CHECK_REQUIRED_LINK_OPTIONS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Extra link options (for CMAKE_REQUIRED_LINK_OPTIONS)"}], "type": "STRING", "value": ""}, {"name": "SDL_CLANG_TIDY", "properties": [{"name": "HELPSTRING", "value": "Run clang-tidy static analysis"}], "type": "BOOL", "value": "OFF"}, {"name": "SDL_CLOCK_GETTIME", "properties": [{"name": "HELPSTRING", "value": "Use clock_gettime() instead of gettimeofday()"}], "type": "BOOL", "value": "OFF"}, {"name": "SDL_CPU_ARM32", "properties": [{"name": "HELPSTRING", "value": "Detected architecture ARM32"}], "type": "BOOL", "value": "0"}, {"name": "SDL_CPU_ARM64", "properties": [{"name": "HELPSTRING", "value": "Detected architecture ARM64"}], "type": "BOOL", "value": "0"}, {"name": "SDL_CPU_ARM64EC", "properties": [{"name": "HELPSTRING", "value": "Detected architecture ARM64EC"}], "type": "BOOL", "value": "0"}, {"name": "SDL_CPU_CHECK_ALL", "properties": [{"name": "HELPSTRING", "value": "Result of TRY_COMPILE"}], "type": "INTERNAL", "value": "TRUE"}, {"name": "SDL_CPU_EMSCRIPTEN", "properties": [{"name": "HELPSTRING", "value": "Detected architecture EMSCRIPTEN"}], "type": "BOOL", "value": "0"}, {"name": "SDL_CPU_LOONGARCH64", "properties": [{"name": "HELPSTRING", "value": "Detected architecture LOONGARCH64"}], "type": "BOOL", "value": "0"}, {"name": "SDL_CPU_POWERPC32", "properties": [{"name": "HELPSTRING", "value": "Detected architecture POWERPC32"}], "type": "BOOL", "value": "0"}, {"name": "SDL_CPU_POWERPC64", "properties": [{"name": "HELPSTRING", "value": "Detected architecture POWERPC64"}], "type": "BOOL", "value": "0"}, {"name": "SDL_CPU_X64", "properties": [{"name": "HELPSTRING", "value": "Detected architecture X64"}], "type": "BOOL", "value": "1"}, {"name": "SDL_CPU_X86", "properties": [{"name": "HELPSTRING", "value": "Detected architecture X86"}], "type": "BOOL", "value": "0"}, {"name": "SDL_DEPS_SHARED", "properties": [{"name": "HELPSTRING", "value": "Load dependencies dynamically"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_DIALOG", "properties": [{"name": "HELPSTRING", "value": "Enable the Dialog subsystem"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_DIRECTX", "properties": [{"name": "HELPSTRING", "value": "Use DirectX for Windows audio/video"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_DISKAUDIO", "properties": [{"name": "HELPSTRING", "value": "Support the disk writer audio driver"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_DUMMYAUDIO", "properties": [{"name": "HELPSTRING", "value": "Support the dummy audio driver"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_DUMMYCAMERA", "properties": [{"name": "HELPSTRING", "value": "Support the dummy camera driver"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_DUMMYVIDEO", "properties": [{"name": "HELPSTRING", "value": "Use dummy video driver"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_EXAMPLES", "properties": [{"name": "HELPSTRING", "value": "Build the examples directory"}], "type": "BOOL", "value": "OFF"}, {"name": "SDL_FOREGROUNDING_SIGNAL", "properties": [{"name": "HELPSTRING", "value": "number to use for magic foregrounding signal or 'OFF'"}], "type": "STRING", "value": "OFF"}, {"name": "SDL_GCC_ATOMICS", "properties": [{"name": "HELPSTRING", "value": "Use gcc builtin atomics"}], "type": "BOOL", "value": "OFF"}, {"name": "SDL_GPU", "properties": [{"name": "HELPSTRING", "value": "Enable the GPU subsystem"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_GPU_DXVK", "properties": [{"name": "HELPSTRING", "value": "Build SDL_GPU with DXVK support"}], "type": "BOOL", "value": "OFF"}, {"name": "SDL_HAPTIC", "properties": [{"name": "HELPSTRING", "value": "Enable the Haptic subsystem"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_HIDAPI", "properties": [{"name": "HELPSTRING", "value": "Enable the HIDAPI subsystem"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_HIDAPI_JOYSTICK", "properties": [{"name": "HELPSTRING", "value": "Use HIDAPI for low level joystick drivers"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_HIDAPI_LIBUSB", "properties": [{"name": "HELPSTRING", "value": "Use libusb for low level joystick drivers"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_HIDAPI_LIBUSB_SHARED", "properties": [{"name": "HELPSTRING", "value": "Dynamically load libusb support"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_INSTALL", "properties": [{"name": "HELPSTRING", "value": "Enable installation of SDL3"}], "type": "BOOL", "value": "OFF"}, {"name": "SDL_INSTALL_CMAKEDIR_ROOT", "properties": [{"name": "HELPSTRING", "value": "Root folder where to install SDL3Config.cmake related files (SDL3 subfolder for MSVC projects)"}], "type": "STRING", "value": "cmake"}, {"name": "SDL_JACK", "properties": [{"name": "HELPSTRING", "value": "Support the JACK audio API"}], "type": "BOOL", "value": "OFF"}, {"name": "SDL_JOYSTICK", "properties": [{"name": "HELPSTRING", "value": "Enable the Joystick subsystem"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_KMSDRM", "properties": [{"name": "HELPSTRING", "value": "Use KMS DRM video driver"}], "type": "BOOL", "value": "OFF"}, {"name": "SDL_LIBC", "properties": [{"name": "HELPSTRING", "value": "Use the system C library"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_LIBICONV", "properties": [{"name": "HELPSTRING", "value": "Prefer iconv() from libiconv, if available, over libc version"}], "type": "BOOL", "value": "OFF"}, {"name": "SDL_LIBUDEV", "properties": [{"name": "HELPSTRING", "value": "Enable libudev support"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_MMX", "properties": [{"name": "HELPSTRING", "value": "Use MMX assembly routines"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_OFFSCREEN", "properties": [{"name": "HELPSTRING", "value": "Use offscreen video driver"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_OPENGL", "properties": [{"name": "HELPSTRING", "value": "Include OpenGL support"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_OPENGLES", "properties": [{"name": "HELPSTRING", "value": "Include OpenGL ES support"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_OPENVR", "properties": [{"name": "HELPSTRING", "value": "Use OpenVR video driver"}], "type": "BOOL", "value": "OFF"}, {"name": "SDL_PIPEWIRE", "properties": [{"name": "HELPSTRING", "value": "Use Pipewire audio"}], "type": "BOOL", "value": "OFF"}, {"name": "SDL_POWER", "properties": [{"name": "HELPSTRING", "value": "Enable the Power subsystem"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_PRESEED", "properties": [{"name": "HELPSTRING", "value": "Preseed CMake cache to speed up configuration"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_PTHREADS", "properties": [{"name": "HELPSTRING", "value": "Use POSIX threads for multi-threading"}], "type": "BOOL", "value": "OFF"}, {"name": "SDL_PULSEAUDIO", "properties": [{"name": "HELPSTRING", "value": "Use PulseAudio"}], "type": "BOOL", "value": "OFF"}, {"name": "SDL_RENDER", "properties": [{"name": "HELPSTRING", "value": "Enable the Render subsystem"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_RENDER_D3D", "properties": [{"name": "HELPSTRING", "value": "Enable the Direct3D 9 render driver"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_RENDER_D3D11", "properties": [{"name": "HELPSTRING", "value": "Enable the Direct3D 11 render driver"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_RENDER_D3D12", "properties": [{"name": "HELPSTRING", "value": "Enable the Direct3D 12 render driver"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_RENDER_GPU", "properties": [{"name": "HELPSTRING", "value": "Enable the SDL_GPU render driver"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_RENDER_METAL", "properties": [{"name": "HELPSTRING", "value": "Enable the Metal render driver"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_RENDER_VULKAN", "properties": [{"name": "HELPSTRING", "value": "Enable the Vulkan render driver"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_REVISION", "properties": [{"name": "HELPSTRING", "value": "Custom SDL revision (only used when REVISION.txt does not exist)"}], "type": "STRING", "value": ""}, {"name": "SDL_RPATH", "properties": [{"name": "HELPSTRING", "value": "Use an rpath when linking SDL"}], "type": "BOOL", "value": "OFF"}, {"name": "SDL_SENSOR", "properties": [{"name": "HELPSTRING", "value": "Enable the Sensor subsystem"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_SHARED", "properties": [{"name": "HELPSTRING", "value": "Build a shared version of the library"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_SNDIO", "properties": [{"name": "HELPSTRING", "value": "Support the sndio audio API"}], "type": "BOOL", "value": "OFF"}, {"name": "SDL_SSE", "properties": [{"name": "HELPSTRING", "value": "Use SSE assembly routines"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_SSE2", "properties": [{"name": "HELPSTRING", "value": "Use SSE2 assembly routines"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_SSE3", "properties": [{"name": "HELPSTRING", "value": "Use SSE3 assembly routines"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_SSE4_1", "properties": [{"name": "HELPSTRING", "value": "Use SSE4.1 assembly routines"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_SSE4_2", "properties": [{"name": "HELPSTRING", "value": "Use SSE4.2 assembly routines"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_STATIC", "properties": [{"name": "HELPSTRING", "value": "Build a static version of the library"}], "type": "BOOL", "value": "OFF"}, {"name": "SDL_SYSTEM_ICONV", "properties": [{"name": "HELPSTRING", "value": "Use iconv() from system-installed libraries"}], "type": "BOOL", "value": "OFF"}, {"name": "SDL_TESTS", "properties": [{"name": "HELPSTRING", "value": "Build the test directory"}], "type": "BOOL", "value": "OFF"}, {"name": "SDL_TESTS_TIMEOUT_MULTIPLIER", "properties": [{"name": "HELPSTRING", "value": "Timeout multiplier to account for really slow machines"}], "type": "STRING", "value": "1"}, {"name": "SDL_TEST_LIBRARY", "properties": [{"name": "HELPSTRING", "value": "Build the SDL3_test library"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_UNINSTALL", "properties": [{"name": "HELPSTRING", "value": "Enable uninstallation of SDL3"}], "type": "BOOL", "value": "OFF"}, {"name": "SDL_VENDOR_INFO", "properties": [{"name": "HELPSTRING", "value": "Vendor name and/or version to add to SDL_REVISION"}], "type": "STRING", "value": ""}, {"name": "SDL_VIDEO", "properties": [{"name": "HELPSTRING", "value": "Enable the Video subsystem"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_VIRTUAL_JOYSTICK", "properties": [{"name": "HELPSTRING", "value": "Enable the virtual-joystick driver"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_VULKAN", "properties": [{"name": "HELPSTRING", "value": "Enable Vulkan support"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_WASAPI", "properties": [{"name": "HELPSTRING", "value": "Use the Windows WASAPI audio driver"}], "type": "BOOL", "value": "ON"}, {"name": "SDL_WAYLAND", "properties": [{"name": "HELPSTRING", "value": "Use Wayland video driver"}], "type": "BOOL", "value": "OFF"}, {"name": "SDL_WERROR", "properties": [{"name": "HELPSTRING", "value": "Enable -<PERSON><PERSON>r"}], "type": "BOOL", "value": "OFF"}, {"name": "SDL_X11", "properties": [{"name": "HELPSTRING", "value": "Use X11 video driver"}], "type": "BOOL", "value": "OFF"}, {"name": "SDL_XINPUT", "properties": [{"name": "HELPSTRING", "value": "Use Xinput for Windows"}], "type": "BOOL", "value": "ON"}, {"name": "_ALLOCA_IN_MALLOC_H", "properties": [{"name": "HELPSTRING", "value": "Have symbol _alloca"}], "type": "INTERNAL", "value": ""}, {"name": "_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX", "properties": [{"name": "HELPSTRING", "value": "CMAKE_INSTALL_PREFIX during last run"}], "type": "INTERNAL", "value": "C:/Program Files (x86)/MySDL3Game"}], "kind": "cache", "version": {"major": 2, "minor": 0}}