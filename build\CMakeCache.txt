# This is the CMakeCache file.
# For build in directory: d:/<PERSON> Work All/CPP/f0/build
# It was generated by CMake: C:/Program Files/CMake/bin/cmake.exe
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Path to a program.
CMAKE_AR:FILEPATH=C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lib.exe

//No help, variable specified on the command line.
CMAKE_BUILD_TYPE:STRING=Debug

//Semicolon separated list of supported configuration types, only
// supports Debug, Release, MinSizeRel, and RelWithDebInfo, anything
// else will be ignored.
CMAKE_CONFIGURATION_TYPES:STRING=Debug;Release;MinSizeRel;RelWithDebInfo

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=/DWIN32 /D_WINDOWS /GR /EHsc

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=/Zi /Ob0 /Od /RTC1

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=/O1 /Ob1 /DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=/O2 /Ob2 /DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=/Zi /O2 /Ob1 /DNDEBUG

//Libraries linked by default with all C++ applications.
CMAKE_CXX_STANDARD_LIBRARIES:STRING=kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=/DWIN32 /D_WINDOWS

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=/Zi /Ob0 /Od /RTC1

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=/O1 /Ob1 /DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=/O2 /Ob2 /DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=/Zi /O2 /Ob1 /DNDEBUG

//Libraries linked by default with all C applications.
CMAKE_C_STANDARD_LIBRARIES:STRING=kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//No help, variable specified on the command line.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=TRUE

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=D:/Frank Work All/CPP/f0/build/CMakeFiles/pkgRedirects

//User executables (bin)
CMAKE_INSTALL_BINDIR:PATH=bin

//Read-only architecture-independent data (DATAROOTDIR)
CMAKE_INSTALL_DATADIR:PATH=

//Read-only architecture-independent data root (share)
CMAKE_INSTALL_DATAROOTDIR:PATH=share

//Documentation root (DATAROOTDIR/doc/PROJECT_NAME)
CMAKE_INSTALL_DOCDIR:PATH=

//C header files (include)
CMAKE_INSTALL_INCLUDEDIR:PATH=include

//Info documentation (DATAROOTDIR/info)
CMAKE_INSTALL_INFODIR:PATH=

//Object code libraries (lib)
CMAKE_INSTALL_LIBDIR:PATH=lib

//Program executables (libexec)
CMAKE_INSTALL_LIBEXECDIR:PATH=libexec

//Locale-dependent data (DATAROOTDIR/locale)
CMAKE_INSTALL_LOCALEDIR:PATH=

//Modifiable single-machine data (var)
CMAKE_INSTALL_LOCALSTATEDIR:PATH=var

//Man documentation (DATAROOTDIR/man)
CMAKE_INSTALL_MANDIR:PATH=

//C header files for non-gcc (/usr/include)
CMAKE_INSTALL_OLDINCLUDEDIR:PATH=/usr/include

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=C:/Program Files (x86)/MySDL3Game

//Run-time variable data (LOCALSTATEDIR/run)
CMAKE_INSTALL_RUNSTATEDIR:PATH=

//System admin executables (sbin)
CMAKE_INSTALL_SBINDIR:PATH=sbin

//Modifiable architecture-independent data (com)
CMAKE_INSTALL_SHAREDSTATEDIR:PATH=com

//Read-only single-machine data (etc)
CMAKE_INSTALL_SYSCONFDIR:PATH=etc

//Path to a program.
CMAKE_LINKER:FILEPATH=C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/link.exe

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//Path to a program.
CMAKE_MT:FILEPATH=CMAKE_MT-NOTFOUND

//Value Computed by CMake
CMAKE_PROJECT_COMPAT_VERSION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=MySDL3Game

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//RC compiler
CMAKE_RC_COMPILER:FILEPATH=rc

//Flags for Windows Resource Compiler during all build types.
CMAKE_RC_FLAGS:STRING=-DWIN32

//Flags for Windows Resource Compiler during DEBUG builds.
CMAKE_RC_FLAGS_DEBUG:STRING=-D_DEBUG

//Flags for Windows Resource Compiler during MINSIZEREL builds.
CMAKE_RC_FLAGS_MINSIZEREL:STRING=

//Flags for Windows Resource Compiler during RELEASE builds.
CMAKE_RC_FLAGS_RELEASE:STRING=

//Flags for Windows Resource Compiler during RELWITHDEBINFO builds.
CMAKE_RC_FLAGS_RELWITHDEBINFO:STRING=

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the archiver during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the archiver during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the archiver during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the archiver during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the archiver during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Git command line client
GIT_EXECUTABLE:FILEPATH=C:/Program Files/Git/cmd/git.exe

//Extra compile options of LibUSB
LibUSB_COMPILE_OPTIONS:STRING=

//Path to a file.
LibUSB_INCLUDE_PATH:PATH=LibUSB_INCLUDE_PATH-NOTFOUND

//Path to a library.
LibUSB_LIBRARY:FILEPATH=LibUSB_LIBRARY-NOTFOUND

//Extra link flags of LibUSB
LibUSB_LINK_FLAGS:STRING=

//Extra link libraries of LibUSB
LibUSB_LINK_LIBRARIES:STRING=

//Value Computed by CMake
MySDL3Game_BINARY_DIR:STATIC=D:/Frank Work All/CPP/f0/build

//Value Computed by CMake
MySDL3Game_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
MySDL3Game_SOURCE_DIR:STATIC=D:/Frank Work All/CPP/f0

//Arguments to supply to pkg-config
PKG_CONFIG_ARGN:STRING=

//pkg-config executable
PKG_CONFIG_EXECUTABLE:FILEPATH=PKG_CONFIG_EXECUTABLE-NOTFOUND

//Value Computed by CMake
SDL3_BINARY_DIR:STATIC=D:/Frank Work All/CPP/f0/build/libs/SDL3

//Value Computed by CMake
SDL3_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
SDL3_SOURCE_DIR:STATIC=D:/Frank Work All/CPP/f0/libs/SDL3

//Support the ALSA audio API
SDL_ALSA:BOOL=OFF

//Use AddressSanitizer to detect memory errors
SDL_ASAN:BOOL=OFF

//Enable assembly routines
SDL_ASSEMBLY:BOOL=ON

//Enable internal sanity checks (auto/disabled/release/enabled/paranoid)
SDL_ASSERTIONS:STRING=auto

//Enable the Audio subsystem
SDL_AUDIO:BOOL=ON

//Use AVX assembly routines
SDL_AVX:BOOL=ON

//Use AVX2 assembly routines
SDL_AVX2:BOOL=ON

//Use AVX512F assembly routines
SDL_AVX512F:BOOL=ON

//number to use for magic backgrounding signal or 'OFF'
SDL_BACKGROUNDING_SIGNAL:STRING=OFF

//Enable the Camera subsystem
SDL_CAMERA:BOOL=ON

//Use Ccache to speed up build
SDL_CCACHE:BOOL=OFF

//Extra includes (for CMAKE_REQUIRED_INCLUDES)
SDL_CHECK_REQUIRED_INCLUDES:STRING=

//Extra link options (for CMAKE_REQUIRED_LINK_OPTIONS)
SDL_CHECK_REQUIRED_LINK_OPTIONS:STRING=

//Run clang-tidy static analysis
SDL_CLANG_TIDY:BOOL=OFF

//Use clock_gettime() instead of gettimeofday()
SDL_CLOCK_GETTIME:BOOL=OFF

//Detected architecture ARM32
SDL_CPU_ARM32:BOOL=0

//Detected architecture ARM64
SDL_CPU_ARM64:BOOL=0

//Detected architecture ARM64EC
SDL_CPU_ARM64EC:BOOL=0

//Detected architecture EMSCRIPTEN
SDL_CPU_EMSCRIPTEN:BOOL=0

//Detected architecture LOONGARCH64
SDL_CPU_LOONGARCH64:BOOL=0

//Detected architecture POWERPC32
SDL_CPU_POWERPC32:BOOL=0

//Detected architecture POWERPC64
SDL_CPU_POWERPC64:BOOL=0

//Detected architecture X64
SDL_CPU_X64:BOOL=1

//Detected architecture X86
SDL_CPU_X86:BOOL=0

//Load dependencies dynamically
SDL_DEPS_SHARED:BOOL=ON

//Enable the Dialog subsystem
SDL_DIALOG:BOOL=ON

//Use DirectX for Windows audio/video
SDL_DIRECTX:BOOL=ON

//Support the disk writer audio driver
SDL_DISKAUDIO:BOOL=ON

//Support the dummy audio driver
SDL_DUMMYAUDIO:BOOL=ON

//Support the dummy camera driver
SDL_DUMMYCAMERA:BOOL=ON

//Use dummy video driver
SDL_DUMMYVIDEO:BOOL=ON

//Build the examples directory
SDL_EXAMPLES:BOOL=OFF

//number to use for magic foregrounding signal or 'OFF'
SDL_FOREGROUNDING_SIGNAL:STRING=OFF

//Use gcc builtin atomics
SDL_GCC_ATOMICS:BOOL=OFF

//Enable the GPU subsystem
SDL_GPU:BOOL=ON

//Build SDL_GPU with DXVK support
SDL_GPU_DXVK:BOOL=OFF

//Enable the Haptic subsystem
SDL_HAPTIC:BOOL=ON

//Enable the HIDAPI subsystem
SDL_HIDAPI:BOOL=ON

//Use HIDAPI for low level joystick drivers
SDL_HIDAPI_JOYSTICK:BOOL=ON

//Use libusb for low level joystick drivers
SDL_HIDAPI_LIBUSB:BOOL=ON

//Dynamically load libusb support
SDL_HIDAPI_LIBUSB_SHARED:BOOL=ON

//Enable installation of SDL3
SDL_INSTALL:BOOL=OFF

//Root folder where to install SDL3Config.cmake related files (SDL3
// subfolder for MSVC projects)
SDL_INSTALL_CMAKEDIR_ROOT:STRING=cmake

//Support the JACK audio API
SDL_JACK:BOOL=OFF

//Enable the Joystick subsystem
SDL_JOYSTICK:BOOL=ON

//Use KMS DRM video driver
SDL_KMSDRM:BOOL=OFF

//Use the system C library
SDL_LIBC:BOOL=ON

//Prefer iconv() from libiconv, if available, over libc version
SDL_LIBICONV:BOOL=OFF

//Enable libudev support
SDL_LIBUDEV:BOOL=ON

//Use MMX assembly routines
SDL_MMX:BOOL=ON

//Use offscreen video driver
SDL_OFFSCREEN:BOOL=ON

//Include OpenGL support
SDL_OPENGL:BOOL=ON

//Include OpenGL ES support
SDL_OPENGLES:BOOL=ON

//Use OpenVR video driver
SDL_OPENVR:BOOL=OFF

//Use Pipewire audio
SDL_PIPEWIRE:BOOL=OFF

//Enable the Power subsystem
SDL_POWER:BOOL=ON

//Preseed CMake cache to speed up configuration
SDL_PRESEED:BOOL=ON

//Use POSIX threads for multi-threading
SDL_PTHREADS:BOOL=OFF

//Use PulseAudio
SDL_PULSEAUDIO:BOOL=OFF

//Enable the Render subsystem
SDL_RENDER:BOOL=ON

//Enable the Direct3D 9 render driver
SDL_RENDER_D3D:BOOL=ON

//Enable the Direct3D 11 render driver
SDL_RENDER_D3D11:BOOL=ON

//Enable the Direct3D 12 render driver
SDL_RENDER_D3D12:BOOL=ON

//Enable the SDL_GPU render driver
SDL_RENDER_GPU:BOOL=ON

//Enable the Metal render driver
SDL_RENDER_METAL:BOOL=ON

//Enable the Vulkan render driver
SDL_RENDER_VULKAN:BOOL=ON

//Custom SDL revision (only used when REVISION.txt does not exist)
SDL_REVISION:STRING=

//Use an rpath when linking SDL
SDL_RPATH:BOOL=OFF

//Enable the Sensor subsystem
SDL_SENSOR:BOOL=ON

//Build a shared version of the library
SDL_SHARED:BOOL=ON

//Support the sndio audio API
SDL_SNDIO:BOOL=OFF

//Use SSE assembly routines
SDL_SSE:BOOL=ON

//Use SSE2 assembly routines
SDL_SSE2:BOOL=ON

//Use SSE3 assembly routines
SDL_SSE3:BOOL=ON

//Use SSE4.1 assembly routines
SDL_SSE4_1:BOOL=ON

//Use SSE4.2 assembly routines
SDL_SSE4_2:BOOL=ON

//Build a static version of the library
SDL_STATIC:BOOL=OFF

//Use iconv() from system-installed libraries
SDL_SYSTEM_ICONV:BOOL=OFF

//Build the test directory
SDL_TESTS:BOOL=OFF

//Timeout multiplier to account for really slow machines
SDL_TESTS_TIMEOUT_MULTIPLIER:STRING=1

//Build the SDL3_test library
SDL_TEST_LIBRARY:BOOL=ON

//Enable uninstallation of SDL3
SDL_UNINSTALL:BOOL=OFF

//Vendor name and/or version to add to SDL_REVISION
SDL_VENDOR_INFO:STRING=

//Enable the Video subsystem
SDL_VIDEO:BOOL=ON

//Enable the virtual-joystick driver
SDL_VIRTUAL_JOYSTICK:BOOL=ON

//Enable Vulkan support
SDL_VULKAN:BOOL=ON

//Use the Windows WASAPI audio driver
SDL_WASAPI:BOOL=ON

//Use Wayland video driver
SDL_WAYLAND:BOOL=OFF

//Enable -Werror
SDL_WERROR:BOOL=OFF

//Use X11 video driver
SDL_X11:BOOL=OFF

//Use Xinput for Windows
SDL_XINPUT:BOOL=ON


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=d:/Frank Work All/CPP/f0/build
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=4
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=1
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=0
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cmake.exe
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cpack.exe
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=C:/Program Files/CMake/bin/ctest.exe
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_STANDARD_LIBRARIES
CMAKE_CXX_STANDARD_LIBRARIES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_STANDARD_LIBRARIES
CMAKE_C_STANDARD_LIBRARIES-ADVANCED:INTERNAL=1
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=Unknown
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Visual Studio 17 2022
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=C:/Program Files/Microsoft Visual Studio/2022/Community
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=D:/Frank Work All/CPP/f0
//ADVANCED property for variable: CMAKE_INSTALL_BINDIR
CMAKE_INSTALL_BINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATADIR
CMAKE_INSTALL_DATADIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATAROOTDIR
CMAKE_INSTALL_DATAROOTDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DOCDIR
CMAKE_INSTALL_DOCDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INCLUDEDIR
CMAKE_INSTALL_INCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INFODIR
CMAKE_INSTALL_INFODIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBDIR
CMAKE_INSTALL_LIBDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBEXECDIR
CMAKE_INSTALL_LIBEXECDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALEDIR
CMAKE_INSTALL_LOCALEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALSTATEDIR
CMAKE_INSTALL_LOCALSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_MANDIR
CMAKE_INSTALL_MANDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_OLDINCLUDEDIR
CMAKE_INSTALL_OLDINCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_RUNSTATEDIR
CMAKE_INSTALL_RUNSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SBINDIR
CMAKE_INSTALL_SBINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SHAREDSTATEDIR
CMAKE_INSTALL_SHAREDSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SYSCONFDIR
CMAKE_INSTALL_SYSCONFDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//Name of CMakeLists files to read
CMAKE_LIST_FILE_NAME:INTERNAL=CMakeLists.txt
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MT
CMAKE_MT-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=2
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//noop for ranlib
CMAKE_RANLIB:INTERNAL=:
//ADVANCED property for variable: CMAKE_RC_COMPILER
CMAKE_RC_COMPILER-ADVANCED:INTERNAL=1
CMAKE_RC_COMPILER_WORKS:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS
CMAKE_RC_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_DEBUG
CMAKE_RC_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_MINSIZEREL
CMAKE_RC_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELEASE
CMAKE_RC_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELWITHDEBINFO
CMAKE_RC_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=C:/Program Files/CMake/share/cmake-4.1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//Test COMPILER_SUPPORTS_AVX
COMPILER_SUPPORTS_AVX:INTERNAL=1
//Test COMPILER_SUPPORTS_AVX2
COMPILER_SUPPORTS_AVX2:INTERNAL=
//Test COMPILER_SUPPORTS_AVX512F
COMPILER_SUPPORTS_AVX512F:INTERNAL=1
//Test COMPILER_SUPPORTS_FDIAGNOSTICS_COLOR_ALWAYS
COMPILER_SUPPORTS_FDIAGNOSTICS_COLOR_ALWAYS:INTERNAL=
//Test COMPILER_SUPPORTS_MMX
COMPILER_SUPPORTS_MMX:INTERNAL=
//Test COMPILER_SUPPORTS_SSE
COMPILER_SUPPORTS_SSE:INTERNAL=1
//Test COMPILER_SUPPORTS_SSE2
COMPILER_SUPPORTS_SSE2:INTERNAL=1
//Test COMPILER_SUPPORTS_SSE3
COMPILER_SUPPORTS_SSE3:INTERNAL=1
//Test COMPILER_SUPPORTS_SSE4_1
COMPILER_SUPPORTS_SSE4_1:INTERNAL=1
//Test COMPILER_SUPPORTS_SSE4_2
COMPILER_SUPPORTS_SSE4_2:INTERNAL=1
//Test /W3
COMPILER_SUPPORTS_W3:INTERNAL=1
//ADVANCED property for variable: GIT_EXECUTABLE
GIT_EXECUTABLE-ADVANCED:INTERNAL=1
//Have include alloca.h
HAVE_ALLOCA_H:INTERNAL=
//Have include audioclient.h
HAVE_AUDIOCLIENT_H:INTERNAL=1
//Have include d3d11_1.h
HAVE_D3D11_H:INTERNAL=1
//Have include d3d9.h
HAVE_D3D9_H:INTERNAL=1
//Have include ddraw.h
HAVE_DDRAW_H:INTERNAL=1
//Have include dinput.h
HAVE_DINPUT_H:INTERNAL=1
//Have include dsound.h
HAVE_DSOUND_H:INTERNAL=1
//Have include dxgi1_6.h
HAVE_DXGI1_6_H:INTERNAL=1
//Have include dxgi.h
HAVE_DXGI_H:INTERNAL=1
//Test HAVE_GAMEINPUT_H
HAVE_GAMEINPUT_H:INTERNAL=
//Have library m
HAVE_LIBM:INTERNAL=
//Have include malloc.h
HAVE_MALLOC:INTERNAL=1
//Have include malloc.h
HAVE_MALLOC_H:INTERNAL=1
//Test HAVE_MFAPI_H
HAVE_MFAPI_H:INTERNAL=1
//Have include mmdeviceapi.h
HAVE_MMDEVICEAPI_H:INTERNAL=1
//Have include roapi.h
HAVE_ROAPI_H:INTERNAL=1
//Have include sensorsapi.h
HAVE_SENSORSAPI_H:INTERNAL=1
//Have include shellscalingapi.h
HAVE_SHELLSCALINGAPI_H:INTERNAL=1
//Have include tpcshrd.h
HAVE_TPCSHRD_H:INTERNAL=1
//Test HAVE_WIN32_CC
HAVE_WIN32_CC:INTERNAL=1
//Test HAVE_WINDOWS_GAMING_INPUT_H
HAVE_WINDOWS_GAMING_INPUT_H:INTERNAL=1
//Test HAVE_XINPUT_H
HAVE_XINPUT_H:INTERNAL=1
//Have symbol abs
LIBC_HAS_ABS:INTERNAL=1
//Have symbol acos
LIBC_HAS_ACOS:INTERNAL=1
//Have symbol acosf
LIBC_HAS_ACOSF:INTERNAL=1
//Have symbol asin
LIBC_HAS_ASIN:INTERNAL=1
//Have symbol asinf
LIBC_HAS_ASINF:INTERNAL=1
//Have symbol atan
LIBC_HAS_ATAN:INTERNAL=1
//Have symbol atan2
LIBC_HAS_ATAN2:INTERNAL=1
//Have symbol atan2f
LIBC_HAS_ATAN2F:INTERNAL=1
//Have symbol atanf
LIBC_HAS_ATANF:INTERNAL=1
//Have symbol atof
LIBC_HAS_ATOF:INTERNAL=1
//Have symbol atoi
LIBC_HAS_ATOI:INTERNAL=1
//Have symbol bcopy
LIBC_HAS_BCOPY:INTERNAL=
//Have symbol calloc
LIBC_HAS_CALLOC:INTERNAL=1
//Have symbol ceil
LIBC_HAS_CEIL:INTERNAL=1
//Have symbol ceilf
LIBC_HAS_CEILF:INTERNAL=1
//Have symbol copysign
LIBC_HAS_COPYSIGN:INTERNAL=1
//Have symbol copysignf
LIBC_HAS_COPYSIGNF:INTERNAL=1
//Have symbol cos
LIBC_HAS_COS:INTERNAL=1
//Have symbol cosf
LIBC_HAS_COSF:INTERNAL=1
//Have symbol exp
LIBC_HAS_EXP:INTERNAL=1
//Have symbol expf
LIBC_HAS_EXPF:INTERNAL=1
//Have symbol fabs
LIBC_HAS_FABS:INTERNAL=1
//Have symbol fabsf
LIBC_HAS_FABSF:INTERNAL=1
//Have include float.h
LIBC_HAS_FLOAT_H:INTERNAL=1
//Have symbol floor
LIBC_HAS_FLOOR:INTERNAL=1
//Have symbol floorf
LIBC_HAS_FLOORF:INTERNAL=1
//Have symbol fmod
LIBC_HAS_FMOD:INTERNAL=1
//Have symbol fmodf
LIBC_HAS_FMODF:INTERNAL=1
//Have symbol fopen64
LIBC_HAS_FOPEN64:INTERNAL=
//Have symbol free
LIBC_HAS_FREE:INTERNAL=1
//Have symbol fseeko
LIBC_HAS_FSEEKO:INTERNAL=
//Have symbol fseeko64
LIBC_HAS_FSEEKO64:INTERNAL=
//Have symbol getenv
LIBC_HAS_GETENV:INTERNAL=1
//Have include iconv.h
LIBC_HAS_ICONV_H:INTERNAL=
//Have symbol index
LIBC_HAS_INDEX:INTERNAL=
//Have include inttypes.h
LIBC_HAS_INTTYPES_H:INTERNAL=1
//Have include isinf(double)
LIBC_HAS_ISINF:INTERNAL=1
//Have include isinff(float)
LIBC_HAS_ISINFF:INTERNAL=
//Have include isnan(double)
LIBC_HAS_ISNAN:INTERNAL=1
//Have include isnanf(float)
LIBC_HAS_ISNANF:INTERNAL=
//Have symbol itoa
LIBC_HAS_ITOA:INTERNAL=1
//Have include limits.h
LIBC_HAS_LIMITS_H:INTERNAL=1
//Have symbol log
LIBC_HAS_LOG:INTERNAL=1
//Have symbol log10
LIBC_HAS_LOG10:INTERNAL=1
//Have symbol log10f
LIBC_HAS_LOG10F:INTERNAL=1
//Have symbol logf
LIBC_HAS_LOGF:INTERNAL=1
//Have symbol lround
LIBC_HAS_LROUND:INTERNAL=1
//Have symbol lroundf
LIBC_HAS_LROUNDF:INTERNAL=1
//Have symbol malloc
LIBC_HAS_MALLOC:INTERNAL=1
//Have include malloc.h
LIBC_HAS_MALLOC_H:INTERNAL=1
//Have include math.h
LIBC_HAS_MATH_H:INTERNAL=1
//Have symbol memcmp
LIBC_HAS_MEMCMP:INTERNAL=1
//Have symbol memcpy
LIBC_HAS_MEMCPY:INTERNAL=1
//Have symbol memmove
LIBC_HAS_MEMMOVE:INTERNAL=1
//Have include memory.h
LIBC_HAS_MEMORY_H:INTERNAL=1
//Have symbol memset
LIBC_HAS_MEMSET:INTERNAL=1
//Have symbol modf
LIBC_HAS_MODF:INTERNAL=1
//Have symbol modff
LIBC_HAS_MODFF:INTERNAL=1
//Have symbol pow
LIBC_HAS_POW:INTERNAL=1
//Have symbol powf
LIBC_HAS_POWF:INTERNAL=1
//Have symbol putenv
LIBC_HAS_PUTENV:INTERNAL=1
//Have symbol realloc
LIBC_HAS_REALLOC:INTERNAL=1
//Have symbol rindex
LIBC_HAS_RINDEX:INTERNAL=
//Have symbol round
LIBC_HAS_ROUND:INTERNAL=1
//Have symbol roundf
LIBC_HAS_ROUNDF:INTERNAL=1
//Have symbol scalbn
LIBC_HAS_SCALBN:INTERNAL=1
//Have symbol scalbnf
LIBC_HAS_SCALBNF:INTERNAL=1
//Have symbol setenv
LIBC_HAS_SETENV:INTERNAL=
//Have include signal.h
LIBC_HAS_SIGNAL_H:INTERNAL=1
//Have symbol sin
LIBC_HAS_SIN:INTERNAL=1
//Have symbol sinf
LIBC_HAS_SINF:INTERNAL=1
//Have symbol sqr
LIBC_HAS_SQR:INTERNAL=
//Have symbol sqrt
LIBC_HAS_SQRT:INTERNAL=1
//Have symbol sqrtf
LIBC_HAS_SQRTF:INTERNAL=1
//Have symbol sscanf
LIBC_HAS_SSCANF:INTERNAL=1
//Have include stdarg.h
LIBC_HAS_STDARG_H:INTERNAL=1
//Have include stdbool.h
LIBC_HAS_STDBOOL_H:INTERNAL=1
//Have include stddef.h
LIBC_HAS_STDDEF_H:INTERNAL=1
//Have include stdint.h
LIBC_HAS_STDINT_H:INTERNAL=1
//Have include stdio.h
LIBC_HAS_STDIO_H:INTERNAL=1
//Have include stdlib.h
LIBC_HAS_STDLIB_H:INTERNAL=1
//Have symbol strchr
LIBC_HAS_STRCHR:INTERNAL=1
//Have symbol strcmp
LIBC_HAS_STRCMP:INTERNAL=1
//Have include strings.h
LIBC_HAS_STRINGS_H:INTERNAL=
//Have include string.h
LIBC_HAS_STRING_H:INTERNAL=1
//Have symbol strlcat
LIBC_HAS_STRLCAT:INTERNAL=
//Have symbol strlcpy
LIBC_HAS_STRLCPY:INTERNAL=
//Have symbol strlen
LIBC_HAS_STRLEN:INTERNAL=1
//Have symbol strncmp
LIBC_HAS_STRNCMP:INTERNAL=1
//Have symbol strnlen
LIBC_HAS_STRNLEN:INTERNAL=1
//Have symbol strnstr
LIBC_HAS_STRNSTR:INTERNAL=
//Have symbol strpbrk
LIBC_HAS_STRPBRK:INTERNAL=1
//Have symbol strrchr
LIBC_HAS_STRRCHR:INTERNAL=1
//Have symbol strstr
LIBC_HAS_STRSTR:INTERNAL=1
//Have symbol strtod
LIBC_HAS_STRTOD:INTERNAL=1
//Have symbol strtok_r
LIBC_HAS_STRTOK_R:INTERNAL=
//Have symbol strtol
LIBC_HAS_STRTOL:INTERNAL=1
//Have symbol strtoll
LIBC_HAS_STRTOLL:INTERNAL=1
//Have symbol strtoul
LIBC_HAS_STRTOUL:INTERNAL=1
//Have symbol strtoull
LIBC_HAS_STRTOULL:INTERNAL=1
//Have include sys/types.h
LIBC_HAS_SYS_TYPES_H:INTERNAL=1
//Have symbol tan
LIBC_HAS_TAN:INTERNAL=1
//Have symbol tanf
LIBC_HAS_TANF:INTERNAL=1
//Have include time.h
LIBC_HAS_TIME_H:INTERNAL=1
//Have symbol trunc
LIBC_HAS_TRUNC:INTERNAL=1
//Have symbol truncf
LIBC_HAS_TRUNCF:INTERNAL=1
//Have symbol unsetenv
LIBC_HAS_UNSETENV:INTERNAL=
//Have symbol vsnprintf
LIBC_HAS_VSNPRINTF:INTERNAL=1
//Have symbol vsscanf
LIBC_HAS_VSSCANF:INTERNAL=1
//Have include wchar.h
LIBC_HAS_WCHAR_H:INTERNAL=1
//Have symbol wcscmp
LIBC_HAS_WCSCMP:INTERNAL=1
//Have symbol wcsdup
LIBC_HAS_WCSDUP:INTERNAL=1
//Have symbol wcslcat
LIBC_HAS_WCSLCAT:INTERNAL=
//Have symbol wcslcpy
LIBC_HAS_WCSLCPY:INTERNAL=
//Have symbol wcslen
LIBC_HAS_WCSLEN:INTERNAL=1
//Have symbol wcsncmp
LIBC_HAS_WCSNCMP:INTERNAL=1
//Have symbol wcsnlen
LIBC_HAS_WCSNLEN:INTERNAL=1
//Have symbol wcsstr
LIBC_HAS_WCSSTR:INTERNAL=1
//Have symbol wcstol
LIBC_HAS_WCSTOL:INTERNAL=1
//Have symbol _copysign
LIBC_HAS__COPYSIGN:INTERNAL=1
//Have symbol _Exit
LIBC_HAS__EXIT:INTERNAL=1
//Have symbol _fseeki64
LIBC_HAS__FSEEKI64:INTERNAL=1
//Have symbol _i64toa
LIBC_HAS__I64TOA:INTERNAL=1
//Have symbol _ltoa
LIBC_HAS__LTOA:INTERNAL=1
//Have symbol _strrev
LIBC_HAS__STRREV:INTERNAL=1
//Have symbol _ui64toa
LIBC_HAS__UI64TOA:INTERNAL=1
//Have symbol _uitoa
LIBC_HAS__UITOA:INTERNAL=
//Have symbol _ultoa
LIBC_HAS__ULTOA:INTERNAL=1
//Have symbol _wcsdup
LIBC_HAS__WCSDUP:INTERNAL=1
//Have include isinf(float)
LIBC_ISINF_HANDLES_FLOAT:INTERNAL=1
//Have include isnan(float)
LIBC_ISNAN_HANDLES_FLOAT:INTERNAL=1
//Have symbol __GLIBC__
LIBC_IS_GLIBC:INTERNAL=
//ADVANCED property for variable: PKG_CONFIG_ARGN
PKG_CONFIG_ARGN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_EXECUTABLE
PKG_CONFIG_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: SDL_CHECK_REQUIRED_INCLUDES
SDL_CHECK_REQUIRED_INCLUDES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: SDL_CHECK_REQUIRED_LINK_OPTIONS
SDL_CHECK_REQUIRED_LINK_OPTIONS-ADVANCED:INTERNAL=1
//Result of TRY_COMPILE
SDL_CPU_CHECK_ALL:INTERNAL=TRUE
//Have symbol _alloca
_ALLOCA_IN_MALLOC_H:INTERNAL=
//CMAKE_INSTALL_PREFIX during last run
_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX:INTERNAL=C:/Program Files (x86)/MySDL3Game

