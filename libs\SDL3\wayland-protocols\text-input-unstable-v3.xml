<?xml version="1.0" encoding="UTF-8"?>

<protocol name="text_input_unstable_v3">
  <copyright>
    Copyright © 2012, 2013 Intel Corporation
    Copyright © 2015, 2016 Jan <PERSON><PERSON>
    Copyright © 2017, 2018 Red Hat, Inc.
    Copyright © 2018       Purism SPC

    Permission to use, copy, modify, distribute, and sell this
    software and its documentation for any purpose is hereby granted
    without fee, provided that the above copyright notice appear in
    all copies and that both that copyright notice and this permission
    notice appear in supporting documentation, and that the name of
    the copyright holders not be used in advertising or publicity
    pertaining to distribution of the software without specific,
    written prior permission.  The copyright holders make no
    representations about the suitability of this software for any
    purpose.  It is provided "as is" without express or implied
    warranty.

    THE COPYRIGHT HOLDERS DISCLAIM ALL WARRANTIES WITH REGARD TO THIS
    SOFTWARE, INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND
    FITNESS, IN NO EVENT SHALL THE COPYRIGHT HOLDERS BE LIABLE FOR ANY
    SPECIAL, INDIRECT OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
    WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN
    AN ACTION OF CONTRACT, <PERSON><PERSON><PERSON><PERSON>ENCE OR OTHER TORTIOUS ACTION,
    <PERSON>ISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF
    THIS SOFTWARE.
  </copyright>

  <description summary="Protocol for composing text">
    This protocol allows compositors to act as input methods and to send text
    to applications. A text input object is used to manage state of what are
    typically text entry fields in the application.

    This document adheres to the RFC 2119 when using words like "must",
    "should", "may", etc.

    Warning! The protocol described in this file is experimental and
    backward incompatible changes may be made. Backward compatible changes
    may be added together with the corresponding interface version bump.
    Backward incompatible changes are done by bumping the version number in
    the protocol and interface names and resetting the interface version.
    Once the protocol is to be declared stable, the 'z' prefix and the
    version number in the protocol and interface names are removed and the
    interface version number is reset.
  </description>

  <interface name="zwp_text_input_v3" version="1">
    <description summary="text input">
      The zwp_text_input_v3 interface represents text input and input methods
      associated with a seat. It provides enter/leave events to follow the
      text input focus for a seat.

      Requests are used to enable/disable the text-input object and set
      state information like surrounding and selected text or the content type.
      The information about the entered text is sent to the text-input object
      via the preedit_string and commit_string events.

      Text is valid UTF-8 encoded, indices and lengths are in bytes. Indices
      must not point to middle bytes inside a code point: they must either
      point to the first byte of a code point or to the end of the buffer.
      Lengths must be measured between two valid indices.

      Focus moving throughout surfaces will result in the emission of
      zwp_text_input_v3.enter and zwp_text_input_v3.leave events. The focused
      surface must commit zwp_text_input_v3.enable and
      zwp_text_input_v3.disable requests as the keyboard focus moves across
      editable and non-editable elements of the UI. Those two requests are not
      expected to be paired with each other, the compositor must be able to
      handle consecutive series of the same request.

      State is sent by the state requests (set_surrounding_text,
      set_content_type and set_cursor_rectangle) and a commit request. After an
      enter event or disable request all state information is invalidated and
      needs to be resent by the client.
    </description>

    <request name="destroy" type="destructor">
      <description summary="Destroy the wp_text_input">
        Destroy the wp_text_input object. Also disables all surfaces enabled
        through this wp_text_input object.
      </description>
    </request>

    <request name="enable">
      <description summary="Request text input to be enabled">
        Requests text input on the surface previously obtained from the enter
        event.

        This request must be issued every time the active text input changes
        to a new one, including within the current surface. Use
        zwp_text_input_v3.disable when there is no longer any input focus on
        the current surface.

        Clients must not enable more than one text input on the single seat
        and should disable the current text input before enabling the new one.
        At most one instance of text input may be in enabled state per instance,
        Requests to enable the another text input when some text input is active
        must be ignored by compositor.

        This request resets all state associated with previous enable, disable,
        set_surrounding_text, set_text_change_cause, set_content_type, and
        set_cursor_rectangle requests, as well as the state associated with
        preedit_string, commit_string, and delete_surrounding_text events.

        The set_surrounding_text, set_content_type and set_cursor_rectangle
        requests must follow if the text input supports the necessary
        functionality.

        State set with this request is double-buffered. It will get applied on
        the next zwp_text_input_v3.commit request, and stay valid until the
        next committed enable or disable request.

        The changes must be applied by the compositor after issuing a
        zwp_text_input_v3.commit request.
      </description>
    </request>

    <request name="disable">
      <description summary="Disable text input on a surface">
        Explicitly disable text input on the current surface (typically when
        there is no focus on any text entry inside the surface).

        State set with this request is double-buffered. It will get applied on
        the next zwp_text_input_v3.commit request.
      </description>
    </request>

    <request name="set_surrounding_text">
      <description summary="sets the surrounding text">
        Sets the surrounding plain text around the input, excluding the preedit
        text.

        The client should notify the compositor of any changes in any of the
        values carried with this request, including changes caused by handling
        incoming text-input events as well as changes caused by other
        mechanisms like keyboard typing.

        If the client is unaware of the text around the cursor, it should not
        issue this request, to signify lack of support to the compositor.

        Text is UTF-8 encoded, and should include the cursor position, the
        complete selection and additional characters before and after them.
        There is a maximum length of wayland messages, so text can not be
        longer than 4000 bytes.

        Cursor is the byte offset of the cursor within text buffer.

        Anchor is the byte offset of the selection anchor within text buffer.
        If there is no selected text, anchor is the same as cursor.

        If any preedit text is present, it is replaced with a cursor for the
        purpose of this event.

        Values set with this request are double-buffered. They will get applied
        on the next zwp_text_input_v3.commit request, and stay valid until the
        next committed enable or disable request.

        The initial state for affected fields is empty, meaning that the text
        input does not support sending surrounding text. If the empty values
        get applied, subsequent attempts to change them may have no effect.
      </description>
      <arg name="text" type="string"/>
      <arg name="cursor" type="int"/>
      <arg name="anchor" type="int"/>
    </request>

    <enum name="change_cause">
      <description summary="text change reason">
        Reason for the change of surrounding text or cursor posision.
      </description>
      <entry name="input_method" value="0" summary="input method caused the change"/>
      <entry name="other" value="1" summary="something else than the input method caused the change"/>
    </enum>

    <request name="set_text_change_cause">
      <description summary="indicates the cause of surrounding text change">
        Tells the compositor why the text surrounding the cursor changed.

        Whenever the client detects an external change in text, cursor, or
        anchor posision, it must issue this request to the compositor. This
        request is intended to give the input method a chance to update the
        preedit text in an appropriate way, e.g. by removing it when the user
        starts typing with a keyboard.

        cause describes the source of the change.

        The value set with this request is double-buffered. It must be applied
        and reset to initial at the next zwp_text_input_v3.commit request.

        The initial value of cause is input_method.
      </description>
      <arg name="cause" type="uint" enum="change_cause"/>
    </request>

    <enum name="content_hint" bitfield="true">
      <description summary="content hint">
        Content hint is a bitmask to allow to modify the behavior of the text
        input.
      </description>
      <entry name="none" value="0x0" summary="no special behavior"/>
      <entry name="completion" value="0x1" summary="suggest word completions"/>
      <entry name="spellcheck" value="0x2" summary="suggest word corrections"/>
      <entry name="auto_capitalization" value="0x4" summary="switch to uppercase letters at the start of a sentence"/>
      <entry name="lowercase" value="0x8" summary="prefer lowercase letters"/>
      <entry name="uppercase" value="0x10" summary="prefer uppercase letters"/>
      <entry name="titlecase" value="0x20" summary="prefer casing for titles and headings (can be language dependent)"/>
      <entry name="hidden_text" value="0x40" summary="characters should be hidden"/>
      <entry name="sensitive_data" value="0x80" summary="typed text should not be stored"/>
      <entry name="latin" value="0x100" summary="just Latin characters should be entered"/>
      <entry name="multiline" value="0x200" summary="the text input is multiline"/>
    </enum>

    <enum name="content_purpose">
      <description summary="content purpose">
        The content purpose allows to specify the primary purpose of a text
        input.

        This allows an input method to show special purpose input panels with
        extra characters or to disallow some characters.
      </description>
      <entry name="normal" value="0" summary="default input, allowing all characters"/>
      <entry name="alpha" value="1" summary="allow only alphabetic characters"/>
      <entry name="digits" value="2" summary="allow only digits"/>
      <entry name="number" value="3" summary="input a number (including decimal separator and sign)"/>
      <entry name="phone" value="4" summary="input a phone number"/>
      <entry name="url" value="5" summary="input an URL"/>
      <entry name="email" value="6" summary="input an email address"/>
      <entry name="name" value="7" summary="input a name of a person"/>
      <entry name="password" value="8" summary="input a password (combine with sensitive_data hint)"/>
      <entry name="pin" value="9" summary="input is a numeric password (combine with sensitive_data hint)"/>
      <entry name="date" value="10" summary="input a date"/>
      <entry name="time" value="11" summary="input a time"/>
      <entry name="datetime" value="12" summary="input a date and time"/>
      <entry name="terminal" value="13" summary="input for a terminal"/>
    </enum>

    <request name="set_content_type">
      <description summary="set content purpose and hint">
        Sets the content purpose and content hint. While the purpose is the
        basic purpose of an input field, the hint flags allow to modify some of
        the behavior.

        Values set with this request are double-buffered. They will get applied
        on the next zwp_text_input_v3.commit request.
        Subsequent attempts to update them may have no effect. The values
        remain valid until the next committed enable or disable request.

        The initial value for hint is none, and the initial value for purpose
        is normal.
      </description>
      <arg name="hint" type="uint" enum="content_hint"/>
      <arg name="purpose" type="uint" enum="content_purpose"/>
    </request>

    <request name="set_cursor_rectangle">
      <description summary="set cursor position">
        Marks an area around the cursor as a x, y, width, height rectangle in
        surface local coordinates.

        Allows the compositor to put a window with word suggestions near the
        cursor, without obstructing the text being input.

        If the client is unaware of the position of edited text, it should not
        issue this request, to signify lack of support to the compositor.

        Values set with this request are double-buffered. They will get applied
        on the next zwp_text_input_v3.commit request, and stay valid until the
        next committed enable or disable request.

        The initial values describing a cursor rectangle are empty. That means
        the text input does not support describing the cursor area. If the
        empty values get applied, subsequent attempts to change them may have
        no effect.
      </description>
      <arg name="x" type="int"/>
      <arg name="y" type="int"/>
      <arg name="width" type="int"/>
      <arg name="height" type="int"/>
    </request>

    <request name="commit">
      <description summary="commit state">
        Atomically applies state changes recently sent to the compositor.

        The commit request establishes and updates the state of the client, and
        must be issued after any changes to apply them.

        Text input state (enabled status, content purpose, content hint,
        surrounding text and change cause, cursor rectangle) is conceptually
        double-buffered within the context of a text input, i.e. between a
        committed enable request and the following committed enable or disable
        request.

        Protocol requests modify the pending state, as opposed to the current
        state in use by the input method. A commit request atomically applies
        all pending state, replacing the current state. After commit, the new
        pending state is as documented for each related request.

        Requests are applied in the order of arrival.

        Neither current nor pending state are modified unless noted otherwise.

        The compositor must count the number of commit requests coming from
        each zwp_text_input_v3 object and use the count as the serial in done
        events.
      </description>
    </request>

    <event name="enter">
      <description summary="enter event">
        Notification that this seat's text-input focus is on a certain surface.

        If client has created multiple text input objects, compositor must send
        this event to all of them.

        When the seat has the keyboard capability the text-input focus follows
        the keyboard focus. This event sets the current surface for the
        text-input object.
      </description>
      <arg name="surface" type="object" interface="wl_surface"/>
    </event>

    <event name="leave">
      <description summary="leave event">
        Notification that this seat's text-input focus is no longer on a
        certain surface. The client should reset any preedit string previously
        set.

        The leave notification clears the current surface. It is sent before
        the enter notification for the new focus. After leave event, compositor
        must ignore requests from any text input instances until next enter
        event.

        When the seat has the keyboard capability the text-input focus follows
        the keyboard focus.
      </description>
      <arg name="surface" type="object" interface="wl_surface"/>
    </event>

    <event name="preedit_string">
      <description summary="pre-edit">
        Notify when a new composing text (pre-edit) should be set at the
        current cursor position. Any previously set composing text must be
        removed. Any previously existing selected text must be removed.

        The argument text contains the pre-edit string buffer.

        The parameters cursor_begin and cursor_end are counted in bytes
        relative to the beginning of the submitted text buffer. Cursor should
        be hidden when both are equal to -1.

        They could be represented by the client as a line if both values are
        the same, or as a text highlight otherwise.

        Values set with this event are double-buffered. They must be applied
        and reset to initial on the next zwp_text_input_v3.done event.

        The initial value of text is an empty string, and cursor_begin,
        cursor_end and cursor_hidden are all 0.
      </description>
      <arg name="text" type="string" allow-null="true"/>
      <arg name="cursor_begin" type="int"/>
      <arg name="cursor_end" type="int"/>
    </event>

    <event name="commit_string">
      <description summary="text commit">
        Notify when text should be inserted into the editor widget. The text to
        commit could be either just a single character after a key press or the
        result of some composing (pre-edit).

        Values set with this event are double-buffered. They must be applied
        and reset to initial on the next zwp_text_input_v3.done event.

        The initial value of text is an empty string.
      </description>
      <arg name="text" type="string" allow-null="true"/>
    </event>

    <event name="delete_surrounding_text">
      <description summary="delete surrounding text">
        Notify when the text around the current cursor position should be
        deleted.

        Before_length and after_length are the number of bytes before and after
        the current cursor index (excluding the selection) to delete.

        If a preedit text is present, in effect before_length is counted from
        the beginning of it, and after_length from its end (see done event
        sequence).

        Values set with this event are double-buffered. They must be applied
        and reset to initial on the next zwp_text_input_v3.done event.

        The initial values of both before_length and after_length are 0.
      </description>
      <arg name="before_length" type="uint" summary="length of text before current cursor position"/>
      <arg name="after_length" type="uint" summary="length of text after current cursor position"/>
    </event>

    <event name="done">
      <description summary="apply changes">
        Instruct the application to apply changes to state requested by the
        preedit_string, commit_string and delete_surrounding_text events. The
        state relating to these events is double-buffered, and each one
        modifies the pending state. This event replaces the current state with
        the pending state.

        The application must proceed by evaluating the changes in the following
        order:

        1. Replace existing preedit string with the cursor.
        2. Delete requested surrounding text.
        3. Insert commit string with the cursor at its end.
        4. Calculate surrounding text to send.
        5. Insert new preedit text in cursor position.
        6. Place cursor inside preedit text.

        The serial number reflects the last state of the zwp_text_input_v3
        object known to the compositor. The value of the serial argument must
        be equal to the number of commit requests already issued on that object.
        When the client receives a done event with a serial different than the
        number of past commit requests, it must proceed as normal, except it
        should not change the current state of the zwp_text_input_v3 object.
      </description>
      <arg name="serial" type="uint"/>
    </event>
  </interface>

  <interface name="zwp_text_input_manager_v3" version="1">
    <description summary="text input manager">
      A factory for text-input objects. This object is a global singleton.
    </description>

    <request name="destroy" type="destructor">
      <description summary="Destroy the wp_text_input_manager">
        Destroy the wp_text_input_manager object.
      </description>
    </request>

    <request name="get_text_input">
      <description summary="create a new text input object">
        Creates a new text-input object for a given seat.
      </description>
      <arg name="id" type="new_id" interface="zwp_text_input_v3"/>
      <arg name="seat" type="object" interface="wl_seat"/>
    </request>
  </interface>
</protocol>
