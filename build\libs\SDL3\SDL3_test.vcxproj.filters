﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\test\SDL_test_assert.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\test\SDL_test_common.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\test\SDL_test_compare.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\test\SDL_test_crc32.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\test\SDL_test_font.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\test\SDL_test_fuzzer.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\test\SDL_test_harness.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\test\SDL_test_log.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\test\SDL_test_md5.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\test\SDL_test_memory.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\Frank Work All\CPP\f0\libs\SDL3\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{DDEB7204-6215-3B87-9EC8-9E7DAEA656E3}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
