<?xml version="1.0" encoding="UTF-8"?>
<protocol name="pointer_warp_v1">
  <copyright>
    Copyright © 2024 Neal Gompa
    Copyright © 2024 Xaver Hugl
    Copyright © 2024 Matthias <PERSON>p
    Copyright © 2024 Vlad <PERSON>

    Permission is hereby granted, free of charge, to any person obtaining a
    copy of this software and associated documentation files (the "Software"),
    to deal in the Software without restriction, including without limitation
    the rights to use, copy, modify, merge, publish, distribute, sublicense,
    and/or sell copies of the Software, and to permit persons to whom the
    Software is furnished to do so, subject to the following conditions:
    The above copyright notice and this permission notice (including the next
    paragraph) shall be included in all copies or substantial portions of the
    Software.
    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL
    THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARIS<PERSON>
    FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
    DEALINGS IN THE SOFTWARE.
  </copyright>

  <interface name="wp_pointer_warp_v1" version="1">
    <description summary="reposition the pointer to a location on a surface">
      This global interface allows applications to request the pointer to be
      moved to a position relative to a wl_surface.

      Note that if the desired behavior is to constrain the pointer to an area
      or lock it to a position, this protocol does not provide a reliable way
      to do that. The pointer constraint and pointer lock protocols should be
      used for those use cases instead.

      Warning! The protocol described in this file is currently in the testing
      phase. Backward compatible changes may be added together with the
      corresponding interface version bump. Backward incompatible changes can
      only be done by creating a new major version of the extension.
    </description>

    <request name="destroy" type="destructor">
      <description summary="destroy the warp manager">
        Destroy the pointer warp manager.
      </description>
    </request>

    <request name="warp_pointer">
      <description summary="reposition the pointer">
        Request the compositor to move the pointer to a surface-local position.
        Whether or not the compositor honors the request is implementation defined,
        but it should
        - honor it if the surface has pointer focus, including
          when it has an implicit pointer grab
        - reject it if the enter serial is incorrect
        - reject it if the requested position is outside of the surface

        Note that the enter serial is valid for any surface of the client,
        and does not have to be from the surface the pointer is warped to.

      </description>
      <arg name="surface" type="object" interface="wl_surface"
           summary="surface to position the pointer on"/>
      <arg name="pointer" type="object" interface="wl_pointer"
           summary="the pointer that should be repositioned"/>
      <arg name="x" type="fixed"/>
      <arg name="y" type="fixed"/>
      <arg name="serial" type="uint" summary="serial number of the enter event"/>
    </request>
  </interface>
</protocol>
