﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{FF43B8D1-D2EC-3783-83DC-76D7D091733E}"
	ProjectSection(ProjectDependencies) = postProject
		{2026BCAB-6569-33BA-8E4C-4CA3E1B9A664} = {2026BCAB-6569-33BA-8E4C-4CA3E1B9A664}
		{B0B431C4-DB32-3B1C-A4EF-62B60D481F0A} = {B0B431C4-DB32-3B1C-A4EF-62B60D481F0A}
		{DDD42229-A28E-334D-8A40-AB5CE37DD9C0} = {DDD42229-A28E-334D-8A40-AB5CE37DD9C0}
		{635B9BB6-FF4C-3E90-A489-8C8EC307D9C2} = {635B9BB6-FF4C-3E90-A489-8C8EC307D9C2}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "SDL3-shared", "SDL3-shared.vcxproj", "{2026BCAB-6569-33BA-8E4C-4CA3E1B9A664}"
	ProjectSection(ProjectDependencies) = postProject
		{DDD42229-A28E-334D-8A40-AB5CE37DD9C0} = {DDD42229-A28E-334D-8A40-AB5CE37DD9C0}
		{635B9BB6-FF4C-3E90-A489-8C8EC307D9C2} = {635B9BB6-FF4C-3E90-A489-8C8EC307D9C2}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "SDL3_test", "SDL3_test.vcxproj", "{B0B431C4-DB32-3B1C-A4EF-62B60D481F0A}"
	ProjectSection(ProjectDependencies) = postProject
		{635B9BB6-FF4C-3E90-A489-8C8EC307D9C2} = {635B9BB6-FF4C-3E90-A489-8C8EC307D9C2}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "SDL_uclibc", "SDL_uclibc.vcxproj", "{DDD42229-A28E-334D-8A40-AB5CE37DD9C0}"
	ProjectSection(ProjectDependencies) = postProject
		{635B9BB6-FF4C-3E90-A489-8C8EC307D9C2} = {635B9BB6-FF4C-3E90-A489-8C8EC307D9C2}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "..\..\\ZERO_CHECK.vcxproj", "{635B9BB6-FF4C-3E90-A489-8C8EC307D9C2}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{FF43B8D1-D2EC-3783-83DC-76D7D091733E}.Debug|x64.ActiveCfg = Debug|x64
		{FF43B8D1-D2EC-3783-83DC-76D7D091733E}.Release|x64.ActiveCfg = Release|x64
		{FF43B8D1-D2EC-3783-83DC-76D7D091733E}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{FF43B8D1-D2EC-3783-83DC-76D7D091733E}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{2026BCAB-6569-33BA-8E4C-4CA3E1B9A664}.Debug|x64.ActiveCfg = Debug|x64
		{2026BCAB-6569-33BA-8E4C-4CA3E1B9A664}.Debug|x64.Build.0 = Debug|x64
		{2026BCAB-6569-33BA-8E4C-4CA3E1B9A664}.Release|x64.ActiveCfg = Release|x64
		{2026BCAB-6569-33BA-8E4C-4CA3E1B9A664}.Release|x64.Build.0 = Release|x64
		{2026BCAB-6569-33BA-8E4C-4CA3E1B9A664}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{2026BCAB-6569-33BA-8E4C-4CA3E1B9A664}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{2026BCAB-6569-33BA-8E4C-4CA3E1B9A664}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{2026BCAB-6569-33BA-8E4C-4CA3E1B9A664}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{B0B431C4-DB32-3B1C-A4EF-62B60D481F0A}.Debug|x64.ActiveCfg = Debug|x64
		{B0B431C4-DB32-3B1C-A4EF-62B60D481F0A}.Debug|x64.Build.0 = Debug|x64
		{B0B431C4-DB32-3B1C-A4EF-62B60D481F0A}.Release|x64.ActiveCfg = Release|x64
		{B0B431C4-DB32-3B1C-A4EF-62B60D481F0A}.Release|x64.Build.0 = Release|x64
		{B0B431C4-DB32-3B1C-A4EF-62B60D481F0A}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{B0B431C4-DB32-3B1C-A4EF-62B60D481F0A}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{B0B431C4-DB32-3B1C-A4EF-62B60D481F0A}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{B0B431C4-DB32-3B1C-A4EF-62B60D481F0A}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{DDD42229-A28E-334D-8A40-AB5CE37DD9C0}.Debug|x64.ActiveCfg = Debug|x64
		{DDD42229-A28E-334D-8A40-AB5CE37DD9C0}.Debug|x64.Build.0 = Debug|x64
		{DDD42229-A28E-334D-8A40-AB5CE37DD9C0}.Release|x64.ActiveCfg = Release|x64
		{DDD42229-A28E-334D-8A40-AB5CE37DD9C0}.Release|x64.Build.0 = Release|x64
		{DDD42229-A28E-334D-8A40-AB5CE37DD9C0}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{DDD42229-A28E-334D-8A40-AB5CE37DD9C0}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{DDD42229-A28E-334D-8A40-AB5CE37DD9C0}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{DDD42229-A28E-334D-8A40-AB5CE37DD9C0}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{635B9BB6-FF4C-3E90-A489-8C8EC307D9C2}.Debug|x64.ActiveCfg = Debug|x64
		{635B9BB6-FF4C-3E90-A489-8C8EC307D9C2}.Debug|x64.Build.0 = Debug|x64
		{635B9BB6-FF4C-3E90-A489-8C8EC307D9C2}.Release|x64.ActiveCfg = Release|x64
		{635B9BB6-FF4C-3E90-A489-8C8EC307D9C2}.Release|x64.Build.0 = Release|x64
		{635B9BB6-FF4C-3E90-A489-8C8EC307D9C2}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{635B9BB6-FF4C-3E90-A489-8C8EC307D9C2}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{635B9BB6-FF4C-3E90-A489-8C8EC307D9C2}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{635B9BB6-FF4C-3E90-A489-8C8EC307D9C2}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {58C3E56B-9545-37FC-ACA1-9B04FBB2FCE5}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
