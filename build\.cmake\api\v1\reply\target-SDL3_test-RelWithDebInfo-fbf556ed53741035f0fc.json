{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "libs/SDL3/RelWithDebInfo/SDL3_test.lib"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "add_compile_options", "target_compile_options", "SDL_AddCommonCompilerFlags", "target_link_libraries", "target_sources"], "files": ["libs/SDL3/CMakeLists.txt", "libs/SDL3/cmake/sdlcompilers.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 472, "parent": 0}, {"command": 1, "file": 0, "line": 19, "parent": 0}, {"command": 3, "file": 0, "line": 474, "parent": 0}, {"command": 2, "file": 1, "line": 37, "parent": 3}, {"command": 4, "file": 0, "line": 3625, "parent": 0}, {"command": 5, "file": 0, "line": 3612, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /Zi /O2 /Ob1 /DNDEBUG -MD"}, {"backtrace": 2, "fragment": "/utf-8"}, {"backtrace": 4, "fragment": "/W3"}], "includes": [{"backtrace": 5, "path": "D:/<PERSON> All/CPP/f0/build/libs/SDL3/include-revision"}, {"backtrace": 5, "path": "D:/<PERSON> All/CPP/f0/libs/SDL3/include"}], "language": "C", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "SDL3_test::@1536e03098708b12d592", "name": "SDL3_test", "nameOnDisk": "SDL3_test.lib", "paths": {"build": "libs/SDL3", "source": "libs/SDL3"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]}], "sources": [{"backtrace": 6, "compileGroupIndex": 0, "path": "libs/SDL3/src/test/SDL_test_assert.c", "sourceGroupIndex": 0}, {"backtrace": 6, "compileGroupIndex": 0, "path": "libs/SDL3/src/test/SDL_test_common.c", "sourceGroupIndex": 0}, {"backtrace": 6, "compileGroupIndex": 0, "path": "libs/SDL3/src/test/SDL_test_compare.c", "sourceGroupIndex": 0}, {"backtrace": 6, "compileGroupIndex": 0, "path": "libs/SDL3/src/test/SDL_test_crc32.c", "sourceGroupIndex": 0}, {"backtrace": 6, "compileGroupIndex": 0, "path": "libs/SDL3/src/test/SDL_test_font.c", "sourceGroupIndex": 0}, {"backtrace": 6, "compileGroupIndex": 0, "path": "libs/SDL3/src/test/SDL_test_fuzzer.c", "sourceGroupIndex": 0}, {"backtrace": 6, "compileGroupIndex": 0, "path": "libs/SDL3/src/test/SDL_test_harness.c", "sourceGroupIndex": 0}, {"backtrace": 6, "compileGroupIndex": 0, "path": "libs/SDL3/src/test/SDL_test_log.c", "sourceGroupIndex": 0}, {"backtrace": 6, "compileGroupIndex": 0, "path": "libs/SDL3/src/test/SDL_test_md5.c", "sourceGroupIndex": 0}, {"backtrace": 6, "compileGroupIndex": 0, "path": "libs/SDL3/src/test/SDL_test_memory.c", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}