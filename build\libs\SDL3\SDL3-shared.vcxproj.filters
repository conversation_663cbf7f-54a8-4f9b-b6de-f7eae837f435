﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\Frank Work All\CPP\f0\build\libs\SDL3\CMakeFiles\SDL3-shared.dir\cmake_pch.cxx">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\build\libs\SDL3\CMakeFiles\SDL3-shared.dir\cmake_pch.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\SDL.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\SDL_assert.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\SDL_error.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\SDL_guid.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\SDL_hashtable.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\SDL_hints.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\SDL_list.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\SDL_log.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\SDL_properties.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\SDL_utils.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\atomic\SDL_atomic.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\atomic\SDL_spinlock.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\audio\SDL_audio.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\audio\SDL_audiocvt.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\audio\SDL_audiodev.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\audio\SDL_audioqueue.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\audio\SDL_audioresample.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\audio\SDL_audiotypecvt.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\audio\SDL_mixer.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\audio\SDL_wave.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\camera\SDL_camera.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\core\SDL_core_unsupported.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\cpuinfo\SDL_cpuinfo.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\dynapi\SDL_dynapi.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\events\SDL_categories.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\events\SDL_clipboardevents.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\events\SDL_displayevents.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\events\SDL_dropevents.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\events\SDL_events.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\events\SDL_eventwatch.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\events\SDL_keyboard.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\events\SDL_keymap.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\events\SDL_keysym_to_keycode.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\events\SDL_keysym_to_scancode.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\events\SDL_mouse.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\events\SDL_pen.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\events\SDL_quit.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\events\SDL_scancode_tables.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\events\SDL_touch.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\events\SDL_windowevents.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\events\imKStoUCS.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\filesystem\SDL_filesystem.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\gpu\SDL_gpu.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\haptic\SDL_haptic.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\hidapi\SDL_hidapi.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\io\SDL_asyncio.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\io\SDL_iostream.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\io\generic\SDL_asyncio_generic.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\joystick\SDL_gamepad.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\joystick\SDL_joystick.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\joystick\SDL_steam_virtual_gamepad.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\joystick\controller_type.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\locale\SDL_locale.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\main\SDL_main_callbacks.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\main\SDL_runapp.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\misc\SDL_url.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\power\SDL_power.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\render\SDL_d3dmath.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\render\SDL_render.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\render\SDL_render_unsupported.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\render\SDL_yuv_sw.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\render\direct3d\SDL_render_d3d.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\render\direct3d\SDL_shaders_d3d.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\render\direct3d11\SDL_render_d3d11.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\render\direct3d11\SDL_shaders_d3d11.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\render\direct3d12\SDL_render_d3d12.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\render\direct3d12\SDL_shaders_d3d12.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\render\gpu\SDL_pipeline_gpu.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\render\gpu\SDL_render_gpu.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\render\gpu\SDL_shaders_gpu.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\render\ngage\SDL_render_ngage.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\render\opengl\SDL_render_gl.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\render\opengl\SDL_shaders_gl.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\render\opengles2\SDL_render_gles2.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\render\opengles2\SDL_shaders_gles2.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\render\ps2\SDL_render_ps2.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\render\psp\SDL_render_psp.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\render\software\SDL_blendfillrect.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\render\software\SDL_blendline.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\render\software\SDL_blendpoint.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\render\software\SDL_drawline.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\render\software\SDL_drawpoint.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\render\software\SDL_render_sw.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\render\software\SDL_rotate.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\render\software\SDL_triangle.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\render\vitagxm\SDL_render_vita_gxm.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\render\vitagxm\SDL_render_vita_gxm_memory.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\render\vitagxm\SDL_render_vita_gxm_tools.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\render\vulkan\SDL_render_vulkan.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\render\vulkan\SDL_shaders_vulkan.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\sensor\SDL_sensor.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\stdlib\SDL_crc16.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\stdlib\SDL_crc32.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\stdlib\SDL_getenv.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\stdlib\SDL_iconv.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\stdlib\SDL_malloc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\stdlib\SDL_memcpy.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\stdlib\SDL_memmove.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\stdlib\SDL_memset.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\stdlib\SDL_mslibc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\stdlib\SDL_murmur3.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\stdlib\SDL_qsort.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\stdlib\SDL_random.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\stdlib\SDL_stdlib.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\stdlib\SDL_string.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\stdlib\SDL_strtokr.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\storage\SDL_storage.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\thread\SDL_thread.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\time\SDL_time.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\timer\SDL_timer.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\SDL_RLEaccel.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\SDL_blit.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\SDL_blit_0.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\SDL_blit_1.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\SDL_blit_A.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\SDL_blit_N.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\SDL_blit_auto.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\SDL_blit_copy.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\SDL_blit_slow.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\SDL_bmp.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\SDL_clipboard.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\SDL_egl.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\SDL_fillrect.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\SDL_pixels.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\SDL_rect.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\SDL_stb.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\SDL_stretch.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\SDL_surface.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\SDL_video.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\SDL_video_unsupported.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\SDL_vulkan_utils.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\SDL_yuv.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\yuv2rgb\yuv_rgb_lsx.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\yuv2rgb\yuv_rgb_sse.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\yuv2rgb\yuv_rgb_std.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\audio\dummy\SDL_dummyaudio.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\audio\disk\SDL_diskaudio.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\camera\dummy\SDL_camera_dummy.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\joystick\virtual\SDL_virtualjoystick.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\dummy\SDL_nullevents.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\dummy\SDL_nullframebuffer.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\dummy\SDL_nullvideo.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\core\windows\SDL_hid.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\core\windows\SDL_immdevice.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\core\windows\SDL_windows.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\core\windows\SDL_xinput.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\core\windows\pch.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\core\windows\SDL_gameinput.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\core\windows\pch_cpp.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\main\windows\SDL_sysmain_runapp.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\io\windows\SDL_asyncio_windows_ioring.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\misc\windows\SDL_sysurl.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\audio\directsound\SDL_directsound.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\audio\wasapi\SDL_wasapi.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\windows\SDL_surface_utils.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\windows\SDL_windowsclipboard.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\windows\SDL_windowsevents.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\windows\SDL_windowsframebuffer.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\windows\SDL_windowskeyboard.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\windows\SDL_windowsmessagebox.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\windows\SDL_windowsmodes.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\windows\SDL_windowsmouse.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\windows\SDL_windowsopengl.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\windows\SDL_windowsopengles.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\windows\SDL_windowsrawinput.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\windows\SDL_windowsshape.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\windows\SDL_windowsvideo.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\windows\SDL_windowsvulkan.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\windows\SDL_windowswindow.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\windows\SDL_windowsgameinput.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\thread\generic\SDL_syscond.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\thread\generic\SDL_sysrwlock.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\thread\windows\SDL_syscond_cv.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\thread\windows\SDL_sysmutex.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\thread\windows\SDL_sysrwlock_srw.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\thread\windows\SDL_syssem.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\thread\windows\SDL_systhread.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\thread\windows\SDL_systls.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\sensor\windows\SDL_windowssensor.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\power\windows\SDL_syspower.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\locale\windows\SDL_syslocale.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\filesystem\windows\SDL_sysfilesystem.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\filesystem\windows\SDL_sysfsops.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\storage\generic\SDL_genericstorage.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\storage\steam\SDL_steamstorage.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\time\windows\SDL_systime.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\timer\windows\SDL_systimer.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\loadso\windows\SDL_sysloadso.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\tray\windows\SDL_tray.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\joystick\hidapi\SDL_hidapi_8bitdo.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\joystick\hidapi\SDL_hidapi_combined.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\joystick\hidapi\SDL_hidapi_flydigi.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\joystick\hidapi\SDL_hidapi_gamecube.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\joystick\hidapi\SDL_hidapi_gip.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\joystick\hidapi\SDL_hidapi_lg4ff.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\joystick\hidapi\SDL_hidapi_luna.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\joystick\hidapi\SDL_hidapi_ps3.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\joystick\hidapi\SDL_hidapi_ps4.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\joystick\hidapi\SDL_hidapi_ps5.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\joystick\hidapi\SDL_hidapi_rumble.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\joystick\hidapi\SDL_hidapi_shield.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\joystick\hidapi\SDL_hidapi_sinput.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\joystick\hidapi\SDL_hidapi_stadia.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\joystick\hidapi\SDL_hidapi_steam.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\joystick\hidapi\SDL_hidapi_steam_hori.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\joystick\hidapi\SDL_hidapi_steamdeck.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\joystick\hidapi\SDL_hidapi_switch.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\joystick\hidapi\SDL_hidapi_wii.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\joystick\hidapi\SDL_hidapi_xbox360.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\joystick\hidapi\SDL_hidapi_xbox360w.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\joystick\hidapi\SDL_hidapi_xboxone.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\joystick\hidapi\SDL_hidapijoystick.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\haptic\hidapi\SDL_hidapihaptic.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\haptic\hidapi\SDL_hidapihaptic_lg4ff.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\joystick\windows\SDL_dinputjoystick.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\joystick\windows\SDL_rawinputjoystick.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\joystick\windows\SDL_windows_gaming_input.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\joystick\windows\SDL_windowsjoystick.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\joystick\windows\SDL_xinputjoystick.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\haptic\windows\SDL_dinputhaptic.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\haptic\windows\SDL_windowshaptic.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\camera\mediafoundation\SDL_camera_mediafoundation.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\dialog\SDL_dialog.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\dialog\SDL_dialog_utils.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\dialog\windows\SDL_windowsdialog.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\process\SDL_process.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\process\windows\SDL_windowsprocess.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\offscreen\SDL_offscreenevents.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\offscreen\SDL_offscreenframebuffer.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\offscreen\SDL_offscreenopengles.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\offscreen\SDL_offscreenvideo.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\offscreen\SDL_offscreenvulkan.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\video\offscreen\SDL_offscreenwindow.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\tray\SDL_tray_utils.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\gpu\d3d12\SDL_gpu_d3d12.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\gpu\vulkan\SDL_gpu_vulkan.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\main\generic\SDL_sysmain_callbacks.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\Frank Work All\CPP\f0\build\libs\SDL3\CMakeFiles\SDL3-shared.dir\Debug\cmake_pch.h">
      <Filter>Precompile Header File</Filter>
    </ClInclude>
    <ClInclude Include="D:\Frank Work All\CPP\f0\build\libs\SDL3\CMakeFiles\SDL3-shared.dir\Debug\cmake_pch.hxx">
      <Filter>Precompile Header File</Filter>
    </ClInclude>
    <ClInclude Include="D:\Frank Work All\CPP\f0\build\libs\SDL3\CMakeFiles\SDL3-shared.dir\Release\cmake_pch.h">
      <Filter>Precompile Header File</Filter>
    </ClInclude>
    <ClInclude Include="D:\Frank Work All\CPP\f0\build\libs\SDL3\CMakeFiles\SDL3-shared.dir\Release\cmake_pch.hxx">
      <Filter>Precompile Header File</Filter>
    </ClInclude>
    <ClInclude Include="D:\Frank Work All\CPP\f0\build\libs\SDL3\CMakeFiles\SDL3-shared.dir\MinSizeRel\cmake_pch.h">
      <Filter>Precompile Header File</Filter>
    </ClInclude>
    <ClInclude Include="D:\Frank Work All\CPP\f0\build\libs\SDL3\CMakeFiles\SDL3-shared.dir\MinSizeRel\cmake_pch.hxx">
      <Filter>Precompile Header File</Filter>
    </ClInclude>
    <ClInclude Include="D:\Frank Work All\CPP\f0\build\libs\SDL3\CMakeFiles\SDL3-shared.dir\RelWithDebInfo\cmake_pch.h">
      <Filter>Precompile Header File</Filter>
    </ClInclude>
    <ClInclude Include="D:\Frank Work All\CPP\f0\build\libs\SDL3\CMakeFiles\SDL3-shared.dir\RelWithDebInfo\cmake_pch.hxx">
      <Filter>Precompile Header File</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\Frank Work All\CPP\f0\libs\SDL3\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="D:\Frank Work All\CPP\f0\libs\SDL3\src\core\windows\version.rc">
      <Filter>Source Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Precompile Header File">
      <UniqueIdentifier>{CE1D56EC-08A2-38EF-9672-D9648E17DECB}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{DDEB7204-6215-3B87-9EC8-9E7DAEA656E3}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
